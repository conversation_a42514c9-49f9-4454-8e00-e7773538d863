#!/usr/bin/env python
"""
测试学生端功能的脚本
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse, Courseware
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_student_dashboard():
    print("=== 测试学生端功能 ===")
    
    # 1. 检查是否有学生用户
    students = User.objects.filter(role='student')
    print(f"学生用户数量: {students.count()}")
    
    if students.exists():
        student = students.first()
        print(f"测试学生: {student.username}")
        
        # 2. 检查考核数据
        assessments = Assessment.objects.filter(is_published=True)
        print(f"已发布的考核数量: {assessments.count()}")
        
        # 3. 检查课件数据
        coursewares = Courseware.objects.all()
        print(f"课件数量: {coursewares.count()}")
        
        # 4. 测试URL访问
        client = Client()
        client.force_login(student)
        
        # 测试学生仪表板
        try:
            response = client.get('/student-dashboard/dashboard/')
            print(f"学生仪表板状态码: {response.status_code}")
        except Exception as e:
            print(f"学生仪表板访问错误: {e}")
        
        # 测试我的考核
        try:
            response = client.get('/student-dashboard/assessments/')
            print(f"我的考核状态码: {response.status_code}")
        except Exception as e:
            print(f"我的考核访问错误: {e}")
        
        # 测试课件库
        try:
            response = client.get('/student-dashboard/courseware_library/')
            print(f"课件库状态码: {response.status_code}")
        except Exception as e:
            print(f"课件库访问错误: {e}")
        
        # 测试在线学习助手
        try:
            response = client.get('/student-dashboard/online_learning_assistant/')
            print(f"在线学习助手状态码: {response.status_code}")
        except Exception as e:
            print(f"在线学习助手访问错误: {e}")
        
        # 测试实时练习评估
        try:
            response = client.get('/student-dashboard/realtime_practice_evaluation/')
            print(f"实时练习评估状态码: {response.status_code}")
        except Exception as e:
            print(f"实时练习评估访问错误: {e}")
    
    else:
        print("没有找到学生用户，请先创建测试数据")

if __name__ == '__main__':
    test_student_dashboard()
