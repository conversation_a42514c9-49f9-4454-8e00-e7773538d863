#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse

User = get_user_model()

def debug_assessment():
    print("=== 调试考核提交问题 ===")
    
    # 1. 获取学生用户
    student = User.objects.filter(role='student').first()
    print(f"学生用户: {student.username}")
    
    # 2. 获取考核6
    assessment = Assessment.objects.get(id=6)
    print(f"考核: {assessment.title}")
    
    # 3. 查看考核的题目
    questions = assessment.questions.all()
    print(f"题目数量: {questions.count()}")
    
    for q in questions:
        print(f"题目 {q.id}: {q.question_text[:50]}...")
        print(f"  类型: {q.question_type}")
        print(f"  选项: {q.options}")
        print(f"  正确答案: {q.answer}")
    
    # 4. 查看现有的回答记录
    existing_responses = StudentResponse.objects.filter(student=student, assessment=assessment)
    print(f"\n现有回答记录数量: {existing_responses.count()}")
    
    for resp in existing_responses:
        print(f"题目 {resp.question.id}: {resp.answer_text} (正确: {resp.is_correct})")
    
    # 5. 模拟提交一个简单的答案
    print("\n=== 模拟提交 ===")
    if questions.exists():
        first_question = questions.first()
        
        # 删除现有记录
        StudentResponse.objects.filter(student=student, assessment=assessment).delete()
        print("删除了现有记录")
        
        # 创建新记录
        test_answer = "A" if first_question.question_type == '选择题' else "测试答案"
        
        response = StudentResponse.objects.create(
            student=student,
            assessment=assessment,
            question=first_question,
            answer_text=test_answer,
            is_correct=False
        )
        print(f"创建了测试记录: {response.id}")
        
        # 验证记录是否存在
        check = StudentResponse.objects.filter(student=student, assessment=assessment).count()
        print(f"验证: 现在有 {check} 条记录")

if __name__ == '__main__':
    debug_assessment()
