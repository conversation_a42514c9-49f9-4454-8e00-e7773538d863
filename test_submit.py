#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse

User = get_user_model()

def test_submit():
    print("=== 测试提交功能 ===")
    
    # 获取学生用户
    student = User.objects.filter(role='student').first()
    print(f"学生: {student.username}")
    
    # 获取考核6
    assessment = Assessment.objects.get(id=6)
    print(f"考核: {assessment.title}")
    
    # 获取题目
    questions = list(assessment.questions.all())
    print(f"题目数量: {len(questions)}")
    
    # 清除现有回答
    StudentResponse.objects.filter(student=student, assessment=assessment).delete()
    print("清除了现有回答")
    
    # 模拟客户端
    client = Client()
    client.force_login(student)
    
    # 准备提交数据
    post_data = {
        'csrfmiddlewaretoken': 'test-token'
    }
    
    for question in questions:
        if question.question_type in ['MC', '选择题']:
            post_data[f'question_{question.id}'] = 'A'
        else:
            post_data[f'question_{question.id}'] = '测试答案'
    
    print(f"提交数据: {post_data}")
    
    # 提交
    response = client.post(f'/student-dashboard/assessment/{assessment.id}/', post_data)
    print(f"响应状态码: {response.status_code}")
    
    if response.status_code == 302:
        print(f"重定向到: {response.url}")
    else:
        print("没有重定向，可能有错误")
        if hasattr(response, 'content'):
            content = response.content.decode('utf-8')
            if 'error' in content:
                print("页面包含错误信息")
    
    # 检查保存的回答
    saved_responses = StudentResponse.objects.filter(student=student, assessment=assessment)
    print(f"保存的回答数量: {saved_responses.count()}")
    
    for resp in saved_responses:
        print(f"  - 题目{resp.question.id}: '{resp.answer_text}' (正确: {resp.is_correct})")

if __name__ == '__main__':
    test_submit()
