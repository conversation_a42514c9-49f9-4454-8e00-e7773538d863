<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}仪表盘{% endblock %}</title>
    <!-- 引入 Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入 Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #f8f9fa;
            color: #343a40;
            margin: 0;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        #sidebar-wrapper {
            min-width: 250px;
            max-width: 250px;
            background-color: #ffffff; /* White background for sidebar */
            color: #343a40;
            transition: margin .25s ease-out;
            padding-top: 20px;
            border-right: 1px solid #e9ecef;
        }
        #sidebar-wrapper .sidebar-heading {
            padding: 0.875rem 1.25rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #0056b3; /* Education Management System blue */
            margin-bottom: 20px;
        }
        #sidebar-wrapper .list-group {
            width: 100%;
        }
        #sidebar-wrapper .list-group-item {
            color: #495057;
            background-color: transparent;
            border: none;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            font-size: 1.05rem;
            position: relative;
        }
        #sidebar-wrapper .list-group-item i {
            margin-right: 10px;
        }
        #sidebar-wrapper .list-group-item.active {
            background-color: #e0f2f7; /* Light blue for active item */
            color: #007bff;
            font-weight: bold;
            border-left: 3px solid #007bff; /* Blue line on active item */
        }
        #sidebar-wrapper .list-group-item:hover:not(.active) {
            color: #007bff;
            background-color: #f1f3f5;
        }
        #page-content-wrapper {
            flex-grow: 1;
            padding: 0;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #ffffff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            padding: 15px 30px;
        }
        .main-content-area {
            padding: 30px;
        }
        /* 添加加载指示器样式 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
        }
        .loading-overlay.active {
            visibility: visible;
            opacity: 1;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <div id="sidebar-wrapper">
            <div class="sidebar-heading">{% block sidebar_heading %}AI助教系统{% endblock %}</div>
            <div class="list-group list-group-flush">
                {% block sidebar_links %}{% endblock %}
            </div>
        </div>
        <!-- /#sidebar-wrapper -->

        <!-- Page Content -->
        <div id="page-content-wrapper">
            <nav class="navbar navbar-expand-lg navbar-light">
                <div class="container-fluid">
                    <div class="ms-auto d-flex align-items-center">
                        <span class="navbar-text me-3">
                            欢迎, {{ user.username }} {% if user.role %}({{ user.role }}){% endif %}
                        </span>
                        <a href="{% url 'users:logout' %}" class="btn btn-outline-danger btn-sm">注销</a>
                    </div>
                </div>
            </nav>

            <div class="main-content-area">
                <div id="content-area" style="position: relative;">
                    <!-- 添加加载指示器 -->
                    <div class="loading-overlay" id="loading-overlay">
                        <div class="loading-spinner"></div>
                    </div>
                    {% block initial_content %}{% endblock %}
                </div>
            </div>
        </div>
        <!-- /#page-content-wrapper -->
    </div>

    {% block modals %}{% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Marked.js for rendering Markdown, loaded once here -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const contentArea = document.getElementById('content-area');
            const loadingOverlay = document.getElementById('loading-overlay');
            const sidebarLinks = document.querySelectorAll('#sidebar-wrapper .list-group-item-action');
            
            let isLoading = false;

            function showLoading() {
                if(loadingOverlay) loadingOverlay.classList.add('active');
                isLoading = true;
            }

            function hideLoading() {
                if(loadingOverlay) loadingOverlay.classList.remove('active');
                isLoading = false;
            }

            function loadContent(url, pushState = true) {
                if (!url || url === '#' || isLoading) return;
                
                showLoading();
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) throw new Error(`网络响应错误: ${response.statusText}`);
                        return response.text();
                    })
                    .then(html => {
                        if (pushState) {
                            history.pushState({path: url}, '', url);
                        }
                        contentArea.innerHTML = html;
                        // After loading new content, re-run any setup scripts if necessary
                        initializeDynamicContent(contentArea);
                        hideLoading();
                    })
                    .catch(error => {
                        console.error('内容加载失败:', error);
                        contentArea.innerHTML = `<div class="alert alert-danger">内容加载失败，请检查网络或联系管理员。</div>`;
                        hideLoading();
                    });
            }

            // --- Event Delegation for dynamically loaded content ---
            contentArea.addEventListener('click', async (event) => {
                const saveBtn = event.target.closest('#save-plan-btn');
                // REMOVED: const generateForm = event.target.closest('#generate-questions-form');

                // 1. Handle "Save Plan" button
                if (saveBtn) {
                    const aiResultsArea = document.getElementById('ai-results-area');
                    const csrfToken = document.querySelector('#upload-form input[name="csrfmiddlewaretoken"]').value;

                    const getPlanData = () => {
                        const content = aiResultsArea.textContent.trim();
                        if (!content || content === 'AI 生成的教案将显示在这里。') {
                            alert('没有可处理的教案内容。');
                            return null;
                        }
                        const title = content.split('\n')[0].trim().substring(0, 80) || 'AI生成的教案';
                        return { title, content };
                    };
                    const planData = getPlanData();
                    if (!planData) return;

                    const originalHtml = saveBtn.innerHTML;
                    saveBtn.disabled = true;
                    saveBtn.innerHTML = `<span class="spinner-border spinner-border-sm"></span> 保存中...`;

                    const formData = new FormData();
                    formData.append('title', planData.title);
                    formData.append('content', planData.content);

                    try {
                        const response = await fetch("{% url 'teacher_dashboard:save_teaching_plan' %}", {
                            method: 'POST',
                            headers: { 'X-CSRFToken': csrfToken },
                            body: formData
                        });
                        const result = await response.json();
                        if (!response.ok || result.status !== 'success') throw new Error(result.message || '保存失败');
                        
                        // SUCCESS: Show the generation section and set the plan ID
                        alert(result.message); // Give user feedback
                        const genSection = document.getElementById('generate-questions-section');
                        const genForm = document.getElementById('generate-questions-form');
                        if (genSection && genForm) {
                            genSection.style.display = 'block';
                            genForm.dataset.planId = result.plan.id;
                            genSection.scrollIntoView({ behavior: 'smooth' });
                        }
                        saveBtn.innerHTML = `<i class="fas fa-check"></i> 已保存`; // Indicate success
                        // No re-enabling, saving is a one-time action for new content

                    } catch (error) {
                        alert(`操作失败: ${error.message}`);
                        saveBtn.disabled = false;
                        saveBtn.innerHTML = originalHtml;
                    }
                }
                
                // MOVED: The form submission logic is now in a 'submit' event listener

                // 3. Handle toggling question type cards
                const questionTypeCheckbox = event.target.closest('input[type="checkbox"][data-question-type]');
                if (questionTypeCheckbox) {
                    const type = questionTypeCheckbox.value;
                    const countInput = contentArea.querySelector(`input[type="number"][data-question-count="${type}"]`);
                    if (countInput) {
                        countInput.disabled = !questionTypeCheckbox.checked;
                    }
                }

                // 4. Handle "Add question to bank" button clicks
                const addQuestionBtn = event.target.closest('.add-question-btn');
                if (addQuestionBtn && !addQuestionBtn.disabled) {
                    event.preventDefault();
                    
                    const button = addQuestionBtn;
                    const { planId, type, question, answer } = button.dataset;

                    if (!planId || !type || !question || !answer) {
                        alert('错误：题目数据不完整，无法添加。');
                        return;
                    }

                    // --- UI loading state ---
                    button.disabled = true;
                    button.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 添加中...`;
                    
                    try {
                        // Use a specific CSRF token from the page, maybe from the initial form
                        const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
                        const response = await fetch("{% url 'teacher_dashboard:add_question_to_bank' %}", {
                             method: 'POST',
                             headers: {
                                 'Content-Type': 'application/json',
                                 'X-CSRFToken': csrfToken
                             },
                             body: JSON.stringify({
                                 plan_id: planId,
                                 question_type: type,
                                 question_text: question,
                                 answer_text: answer,
                             })
                        });
                        const result = await response.json();
                        if (!response.ok || result.status !== 'success') {
                            throw new Error(result.message || '添加失败');
                        }

                        // --- UI success state ---
                        button.innerHTML = `<i class="fas fa-check"></i> 已添加`;
                        button.classList.remove('btn-primary');
                        button.classList.add('btn-success');

                    } catch (error) {
                        alert(`添加失败: ${error.message}`);
                        // --- UI error state ---
                        button.disabled = false;
                        button.innerHTML = '添加到题库';
                    }
                }
            });

            // --- Event Delegation for Form Submissions ---
            contentArea.addEventListener('submit', async (event) => {
                // Handle AI Lesson Plan Generation
                if (event.target.id === 'upload-form') {
                    event.preventDefault();
                    
                    const uploadForm = event.target;
                    const generateBtn = uploadForm.querySelector('#generate-btn');
                    const fileInput = uploadForm.querySelector('#teaching-document-input');
                    const errorArea = uploadForm.nextElementSibling; // Assumes error area is sibling
                    const resultsArea = document.getElementById('ai-results-area'); // It might be outside the form

                    if (fileInput.files.length === 0) {
                        if(errorArea) {
                            errorArea.textContent = '请先选择要上传的文件。';
                            errorArea.style.display = 'block';
                        }
                        return;
                    }
                    
                    // --- UI functions for this specific form ---
                    const showButtonLoading = (isLoading) => {
                        const spinner = generateBtn.querySelector('.spinner-border');
                        const buttonText = generateBtn.querySelector('.button-text');
                        if (isLoading) {
                            generateBtn.disabled = true;
                            spinner.style.display = 'inline-block';
                            buttonText.textContent = '正在生成...';
                            if (errorArea) errorArea.style.display = 'none';
                            if (resultsArea) resultsArea.innerHTML = '<p class="text-muted">AI 生成的教案将显示在这里。</p>';
                        } else {
                            generateBtn.disabled = false;
                            spinner.style.display = 'none';
                            buttonText.textContent = '上传并生成教案';
                        }
                    };
                    
                    showButtonLoading(true);
                    
                    const formData = new FormData(uploadForm);
                    const url = "{% url 'teacher_dashboard:prepare_design' %}";

                    try {
                        const response = await fetch(url, {
                            method: 'POST',
                            body: formData,
                        });

                        const data = await response.json();

                        if (!response.ok) {
                            throw new Error(data.message || `服务器错误: ${response.status}`);
                        }

                        if (data.status === 'success') {
                            if (resultsArea && typeof marked !== 'undefined') {
                                resultsArea.innerHTML = marked.parse(data.preparation_suggestions);
                            }
                            if (errorArea) errorArea.style.display = 'none';
                        } else {
                            if (errorArea) {
                                errorArea.textContent = data.message || '发生未知错误。';
                                errorArea.style.display = 'block';
                            }
                        }
                    } catch (error) {
                        console.error('生成教案时出错:', error);
                        if (errorArea) {
                            errorArea.textContent = error.message || '无法连接到服务器或发生未知错误。';
                            errorArea.style.display = 'block';
                        }
                    } finally {
                        showButtonLoading(false);
                    }
                }

                // Handle Generate Questions Form Submission
                if (event.target.id === 'generate-questions-form') {
                    event.preventDefault(); // Prevent default form submission
                    const generateForm = event.target;
                    
                    const requests = {};
                    const typeCheckboxes = generateForm.querySelectorAll('input[type="checkbox"][data-question-type]');
                    typeCheckboxes.forEach(cb => {
                        if (cb.checked) {
                            const type = cb.value;
                            const countInput = generateForm.querySelector(`input[type="number"][data-question-count="${type}"]`);
                            if (countInput && countInput.value > 0) {
                                requests[type] = countInput.value;
                            }
                        }
                    });

                    if (Object.keys(requests).length === 0) {
                        alert('请至少选择一个有效的题型和数量。');
                        return;
                    }

                    const generateBtn = generateForm.querySelector('#generate-questions-btn');
                    const planId = generateForm.dataset.planId;
                    if (!planId) {
                        alert('错误：没有关联的教案ID。请先保存教案。');
                        return;
                    }

                    const btnText = generateBtn.querySelector('.button-text');
                    const btnSpinner = generateBtn.querySelector('.spinner-border');
                    const originalBtnText = btnText.textContent;
                    generateBtn.disabled = true;
                    btnSpinner.style.display = 'inline-block';
                    btnText.textContent = '生成中...';

                    try {
                        const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
                        const response = await fetch(`/teacher-dashboard/teaching-plan/${planId}/generate-questions/`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': csrfToken
                            },
                            body: JSON.stringify({ requests })
                        });
                        const result = await response.json();
                        if (!response.ok || result.status !== 'success') throw new Error(result.message || '生成失败');

                        alert('题目生成成功！');
                        const questionsContainer = document.getElementById('generated-questions-container');
                        if (questionsContainer && result.questions) {
                            let questionsHtml = '<h3>基于该教案新生成的题目</h3>';
                            result.questions.forEach(q => {
                                const qType = (q.type || '').replace(/"/g, '&quot;');
                                const qQuestion = (q.question || '').replace(/"/g, '&quot;');
                                const qAnswer = (q.answer || '').replace(/"/g, '&quot;');

                                let questionBodyHtml = `<p class="card-text"><strong>题目内容:</strong> ${q.question || '无内容'}</p>`;

                                // If the question is a Multiple Choice and has options, format them into a list.
                                if (q.type === 'MC' && Array.isArray(q.options) && q.options.length > 0) {
                                    const optionsHtml = q.options.map((opt, index) => 
                                        `<li class="list-group-item">${String.fromCharCode(65 + index)}. ${opt}</li>`
                                    ).join('');
                                    questionBodyHtml += `<ul class="list-group list-group-flush mt-2">${optionsHtml}</ul>`;
                                }

                                questionsHtml += `
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <p class="card-text"><strong>题目类型:</strong> ${q.type || 'N/A'}</p>
                                            ${questionBodyHtml}
                                            <p class="card-text mt-2"><strong>参考答案:</strong> ${q.answer || '无内容'}</p>
                                            <button class="btn btn-sm btn-primary add-question-btn" 
                                                    data-plan-id="${planId}"
                                                    data-type="${qType}" 
                                                    data-question="${qQuestion}" 
                                                    data-answer="${qAnswer}">
                                                添加到题库
                                            </button>
                                        </div>
                                    </div>
                                `;
                            });
                            questionsContainer.innerHTML = questionsHtml;
                            questionsContainer.style.display = 'block';
                            questionsContainer.scrollIntoView({ behavior: 'smooth' });
                        }

                    } catch(error) {
                        alert(`生成题目失败: ${error.message}`);
                    } finally {
                        generateBtn.disabled = false;
                        btnSpinner.style.display = 'none';
                        btnText.textContent = originalBtnText;
                    }
                }
            });

            sidebarLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    
                    // 如果正在加载，忽略点击
                    if (isLoading) return;
                    
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                    
                    const url = this.dataset.targetUrl;
                    loadContent(url);
                });
            });

            // Handle browser back/forward buttons
            window.addEventListener('popstate', function(event) {
                if (event.state && event.state.path) {
                    // Don't push state, just load content
                    loadContent(event.state.path, false); 
                }
            });

            // Initial load logic:
            // The shell is always empty now, so we always load the content for the active link.
            const activeLink = document.querySelector('#sidebar-wrapper .list-group-item-action.active');
            if (activeLink) {
                const initialUrl = activeLink.dataset.targetUrl;
                if (initialUrl && initialUrl !== '#') {
                    // Load content but don't add a new history entry for the initial page
                    loadContent(initialUrl, false); 
                }
            }
        });

        function initializeDynamicContent(container) {
            // Initializer for Question Bank Page
            const questionListContainer = container.querySelector('#questions-list-container');
            if (questionListContainer) {
                const selectAllCheckbox = document.getElementById('select-all-checkbox');
                const publishBtn = document.getElementById('publish-assessment-btn');
                
                function getVisibleCheckboxes() {
                    return questionListContainer.querySelectorAll('.question-checkbox');
                }

                function togglePublishButton() {
                    if (!publishBtn) return;
                    const anyChecked = Array.from(getVisibleCheckboxes()).some(cb => cb.checked);
                    publishBtn.disabled = !anyChecked;
                }
                
                questionListContainer.addEventListener('change', (e) => {
                    if (e.target.classList.contains('question-checkbox')) {
                        togglePublishButton();
                        if (!e.target.checked) {
                            selectAllCheckbox.checked = false;
                        } else {
                            const allChecked = Array.from(getVisibleCheckboxes()).every(cb => cb.checked);
                            selectAllCheckbox.checked = allChecked;
                        }
                    }
                });

                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', (e) => {
                        getVisibleCheckboxes().forEach(checkbox => {
                            checkbox.checked = e.target.checked;
                        });
                        togglePublishButton();
                    });
                }
                
                // Also handle publish button click logic here if it's not already delegated
                if (publishBtn) {
                   publishBtn.addEventListener('click', async () => {
                        const selectedQuestionIds = Array.from(getVisibleCheckboxes())
                            .filter(cb => cb.checked)
                            .map(cb => cb.value);

                        if (selectedQuestionIds.length === 0) return; // Should be disabled, but as a safeguard

                        const title = prompt('请输入本次考核的标题（例如：第一单元摸底测试）：', `课堂练习 - ${new Date().toLocaleDateString()}`);
                        if (title === null || title.trim() === '') return;
                        
                        // This part needs CSRF token, which should be available somewhere in the base template or fetched.
                        // For simplicity, let's assume it can be found. A better way is to store it in a meta tag.
                        const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]')?.value || '{{ csrf_token }}';

                        try {
                            const response = await fetch("{% url 'teacher_dashboard:publish_assessment_api' %}", {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRFToken': csrfToken
                                },
                                body: JSON.stringify({
                                    title: title,
                                    question_ids: selectedQuestionIds
                                })
                            });
                            const result = await response.json();
                            if (!response.ok) throw new Error(result.message || '发布失败');
                            alert(result.message);
                            
                            // Uncheck all after success
                            if(selectAllCheckbox) selectAllCheckbox.checked = false;
                            getVisibleCheckboxes().forEach(cb => cb.checked = false);
                            togglePublishButton();

                        } catch (error) {
                            alert(`操作失败：${error.message}`);
                        }
                    });
                }
            }
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 