<!DOCTYPE html>
<html>
<head>
    <title>AI教学实训模型系统 - 功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
        }
        .hero-section {
            padding: 80px 0;
            color: white;
            text-align: center;
        }
        .demo-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: 100%;
        }
        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        .demo-card .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 30px 20px;
        }
        .demo-card .card-body {
            padding: 30px 20px;
            background: white;
        }
        .demo-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .feature-list {
            text-align: left;
            margin: 20px 0;
        }
        .feature-list li {
            margin: 8px 0;
            color: #666;
        }
        .btn-demo {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .system-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            backdrop-filter: blur(10px);
        }
        .footer-section {
            background: rgba(0,0,0,0.1);
            padding: 40px 0;
            margin-top: 60px;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <h1 class="display-3 fw-bold mb-4">
                <i class="fas fa-graduation-cap me-3"></i>
                AI教学实训模型系统
            </h1>
            <p class="lead fs-4 mb-5">智能化教学平台，三端协同，全面提升教学效率与学习体验</p>
            
            <!-- System Overview -->
            <div class="system-info">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fas fa-users me-2"></i>多角色支持</h5>
                        <p>学生、教师、管理员三端设计</p>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-brain me-2"></i>AI智能助手</h5>
                        <p>智能问答、个性化学习、自动出题</p>
                    </div>
                    <div class="col-md-4">
                        <h5><i class="fas fa-chart-line me-2"></i>数据驱动</h5>
                        <p>学习分析、教学优化、决策支持</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Cards Section -->
    <div class="container pb-5">
        <div class="row g-4">
            <!-- 学生端 -->
            <div class="col-lg-4 col-md-6">
                <div class="card demo-card">
                    <div class="card-header text-center">
                        <div class="demo-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="mb-0">学生端</h3>
                        <p class="mb-0 mt-2">智能化学习体验</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check text-success me-2"></i>我的考核 - 在线测试与成绩查看</li>
                            <li><i class="fas fa-check text-success me-2"></i>在线学习助手 - AI智能问答</li>
                            <li><i class="fas fa-check text-success me-2"></i>实时练习评估 - 个性化练习</li>
                            <li><i class="fas fa-check text-success me-2"></i>学习资源库 - 课件资源管理</li>
                        </ul>
                        <div class="text-center mt-4">
                            <a href="/student-dashboard/demo/" class="btn btn-demo">
                                <i class="fas fa-play me-2"></i>体验学生端
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 教师端 -->
            <div class="col-lg-4 col-md-6">
                <div class="card demo-card">
                    <div class="card-header text-center">
                        <div class="demo-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3 class="mb-0">教师端</h3>
                        <p class="mb-0 mt-2">智能化教学工具</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check text-success me-2"></i>备课设计 - AI辅助教案生成</li>
                            <li><i class="fas fa-check text-success me-2"></i>题库管理 - 智能题目生成</li>
                            <li><i class="fas fa-check text-success me-2"></i>考核发布 - 在线考试管理</li>
                            <li><i class="fas fa-check text-success me-2"></i>数据分析 - 学情数据统计</li>
                        </ul>
                        <div class="text-center mt-4">
                            <a href="/teacher-dashboard/demo/" class="btn btn-demo">
                                <i class="fas fa-play me-2"></i>体验教师端
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理端 -->
            <div class="col-lg-4 col-md-6">
                <div class="card demo-card">
                    <div class="card-header text-center">
                        <div class="demo-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="mb-0">管理端</h3>
                        <p class="mb-0 mt-2">系统管理与监控</p>
                    </div>
                    <div class="card-body">
                        <ul class="feature-list">
                            <li><i class="fas fa-check text-success me-2"></i>用户管理 - 账户权限管理</li>
                            <li><i class="fas fa-check text-success me-2"></i>系统监控 - 运行状态监控</li>
                            <li><i class="fas fa-check text-success me-2"></i>数据统计 - 全局数据分析</li>
                            <li><i class="fas fa-check text-success me-2"></i>系统设置 - 参数配置管理</li>
                        </ul>
                        <div class="text-center mt-4">
                            <a href="/admin-dashboard/demo/" class="btn btn-demo">
                                <i class="fas fa-play me-2"></i>体验管理端
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card demo-card">
                    <div class="card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-rocket me-2"></i>快速访问
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <a href="/users/login/" class="btn btn-outline-primary btn-lg w-100 mb-3">
                                    <i class="fas fa-sign-in-alt me-2"></i>登录系统
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/student-dashboard/test/" class="btn btn-outline-success btn-lg w-100 mb-3">
                                    <i class="fas fa-vial me-2"></i>学生端测试
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/teacher-dashboard/" class="btn btn-outline-warning btn-lg w-100 mb-3">
                                    <i class="fas fa-chalkboard me-2"></i>教师工作台
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="/admin-dashboard/dashboard/" class="btn btn-outline-info btn-lg w-100 mb-3">
                                    <i class="fas fa-tachometer-alt me-2"></i>管理后台
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer-section">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-code me-2"></i>
                AI教学实训模型系统 - 基于Django + AI技术构建
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
