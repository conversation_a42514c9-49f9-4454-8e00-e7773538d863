#!/usr/bin/env python
"""
初始化学生端测试数据
"""
import os
import django
from django.core.files.base import ContentFile

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse, Courseware
from django.contrib.auth.hashers import make_password

User = get_user_model()

def create_test_data():
    print("=== 创建学生端测试数据 ===")
    
    # 1. 创建教师用户
    teacher, created = User.objects.get_or_create(
        username='teacher1',
        defaults={
            'email': '<EMAIL>',
            'password': make_password('password123'),
            'role': 'teacher',
            'first_name': '张',
            'last_name': '老师'
        }
    )
    if created:
        print(f"创建教师用户: {teacher.username}")
    else:
        print(f"教师用户已存在: {teacher.username}")
    
    # 2. 创建学生用户
    student, created = User.objects.get_or_create(
        username='student1',
        defaults={
            'email': '<EMAIL>',
            'password': make_password('password123'),
            'role': 'student',
            'first_name': '李',
            'last_name': '同学'
        }
    )
    if created:
        print(f"创建学生用户: {student.username}")
    else:
        print(f"学生用户已存在: {student.username}")
    
    # 3. 创建题目
    questions_data = [
        {
            'question_text': 'Python是一种什么类型的编程语言？\nA. 编译型语言\nB. 解释型语言\nC. 汇编语言\nD. 机器语言',
            'question_type': 'MC',
            'correct_answer': 'B',
            'explanation': 'Python是一种解释型、面向对象的高级编程语言'
        },
        {
            'question_text': 'HTML是超文本标记语言的缩写。',
            'question_type': 'TF',
            'correct_answer': 'True',
            'explanation': 'HTML确实是HyperText Markup Language的缩写'
        },
        {
            'question_text': '请简述面向对象编程的三大特性。',
            'question_type': 'SA',
            'correct_answer': '封装、继承、多态',
            'explanation': '面向对象编程的三大特性是封装、继承和多态'
        },
        {
            'question_text': 'CSS用于控制网页样式吗？',
            'question_type': 'TF',
            'correct_answer': 'True',
            'explanation': 'CSS层叠样式表用于控制网页的外观和布局'
        }
    ]
    
    questions = []
    for q_data in questions_data:
        question, created = Question.objects.get_or_create(
            question_text=q_data['question_text'],
            defaults={
                'question_type': q_data['question_type'],
                'correct_answer': q_data['correct_answer'],
                'explanation': q_data['explanation'],
                'teacher': teacher
            }
        )
        questions.append(question)
        if created:
            print(f"创建题目: {question.question_text[:30]}...")
    
    # 4. 创建考核
    assessment, created = Assessment.objects.get_or_create(
        title='Python基础测试',
        defaults={
            'teacher': teacher,
            'is_published': True
        }
    )
    if created:
        assessment.questions.set(questions[:3])
        print(f"创建考核: {assessment.title}")
    else:
        print(f"考核已存在: {assessment.title}")
    
    # 5. 创建一些学生答题记录（包括错题）
    if not StudentResponse.objects.filter(student=student, assessment=assessment).exists():
        # 第一题答错
        StudentResponse.objects.create(
            student=student,
            assessment=assessment,
            question=questions[0],
            answer_text='A',
            is_correct=False
        )
        
        # 第二题答对
        StudentResponse.objects.create(
            student=student,
            assessment=assessment,
            question=questions[1],
            answer_text='True',
            is_correct=True
        )
        
        print("创建学生答题记录")
    
    # 6. 创建课件
    courseware, created = Courseware.objects.get_or_create(
        title='Python入门教程',
        defaults={
            'description': 'Python编程语言基础入门课程',
            'teacher': teacher,
            'subject': 'Python编程',
            'file': ContentFile(b'这是一个示例课件内容', name='python_tutorial.txt')
        }
    )
    if created:
        print(f"创建课件: {courseware.title}")
    else:
        print(f"课件已存在: {courseware.title}")
    
    print("=== 测试数据创建完成 ===")
    print(f"教师用户: {teacher.username} (密码: password123)")
    print(f"学生用户: {student.username} (密码: password123)")
    print(f"考核数量: {Assessment.objects.count()}")
    print(f"题目数量: {Question.objects.count()}")
    print(f"课件数量: {Courseware.objects.count()}")
    print(f"学生答题记录: {StudentResponse.objects.count()}")

if __name__ == '__main__':
    create_test_data()
