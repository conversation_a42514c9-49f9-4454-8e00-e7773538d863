#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from teacher_dashboard.models import Assessment, Question, StudentResponse

User = get_user_model()

def test_assessment_submission():
    print("=== 测试考核提交功能 ===")
    
    # 1. 获取测试用户
    try:
        student = User.objects.filter(role='student').first()
        if not student:
            print("❌ 没有找到学生用户")
            return
        print(f"✅ 找到学生用户: {student.username}")
    except Exception as e:
        print(f"❌ 获取学生用户失败: {e}")
        return
    
    # 2. 获取测试考核
    try:
        assessment = Assessment.objects.filter(is_published=True).first()
        if not assessment:
            print("❌ 没有找到已发布的考核")
            return
        print(f"✅ 找到考核: {assessment.title} (ID: {assessment.id})")
    except Exception as e:
        print(f"❌ 获取考核失败: {e}")
        return
    
    # 3. 获取考核题目
    questions = assessment.questions.all()
    print(f"✅ 考核包含 {questions.count()} 道题目")
    
    # 4. 清除之前的回答记录
    StudentResponse.objects.filter(student=student, assessment=assessment).delete()
    print("✅ 清除了之前的回答记录")
    
    # 5. 模拟提交考核
    client = Client()
    client.force_login(student)
    
    # 准备提交数据
    post_data = {'csrfmiddlewaretoken': 'test'}
    for question in questions:
        if question.question_type == '选择题' and question.options:
            # 选择第一个选项
            post_data[f'question_{question.id}'] = question.options[0]
        else:
            # 简答题随便填写
            post_data[f'question_{question.id}'] = '测试答案'
    
    print(f"✅ 准备提交数据: {len(post_data)-1} 个答案")
    
    # 6. 提交考核
    try:
        response = client.post(f'/student-dashboard/assessment/{assessment.id}/', post_data)
        print(f"✅ 提交响应状态码: {response.status_code}")
        
        if response.status_code == 302:
            print(f"✅ 重定向到: {response.url}")
        else:
            print(f"❌ 未重定向，可能有错误")
            
    except Exception as e:
        print(f"❌ 提交失败: {e}")
        return
    
    # 7. 检查保存的回答
    saved_responses = StudentResponse.objects.filter(student=student, assessment=assessment)
    print(f"✅ 保存的回答数量: {saved_responses.count()}")
    
    for resp in saved_responses:
        print(f"   - 题目 {resp.question.id}: '{resp.answer_text}' (正确: {resp.is_correct})")
    
    # 8. 测试结果页面
    try:
        result_response = client.get(f'/student-dashboard/assessment/{assessment.id}/result/')
        print(f"✅ 结果页面状态码: {result_response.status_code}")
        
        if result_response.status_code == 200:
            print("✅ 结果页面加载成功")
        else:
            print(f"❌ 结果页面加载失败")
            
    except Exception as e:
        print(f"❌ 访问结果页面失败: {e}")

if __name__ == '__main__':
    test_assessment_submission()
