from django.db import models
from django.conf import settings
from django.utils import timezone

class Question(models.Model):
    QUESTION_TYPE_CHOICES = (
        ('MC', 'Multiple Choice'),
        ('TF', 'True/False'),
        ('SA', 'Short Answer'),
        ('PG', 'Programming Question'), # Added Programming Question type
    )
    question_text = models.TextField()
    question_type = models.CharField(max_length=2, choices=QUESTION_TYPE_CHOICES)
    correct_answer = models.TextField(help_text="The correct answer for the question.")
    explanation = models.TextField(blank=True, null=True) # Added explanation field
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='questions', null=True, blank=True) # Added teacher foreign key
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True) # Added created_at field
    teaching_plan = models.ForeignKey('TeachingPlan', on_delete=models.SET_NULL, null=True, blank=True, related_name='generated_questions')

    @property
    def answer(self):
        """为了兼容性，提供answer属性"""
        return self.correct_answer

    @property
    def options(self):
        """解析选择题选项"""
        if self.question_type in ['MC', '选择题'] and '\n' in self.correct_answer:
            # 如果答案中包含选项，解析出来
            lines = self.correct_answer.split('\n')
            options = []
            for line in lines:
                if line.strip() and ('.' in line or ')' in line):
                    options.append(line.strip())
            return options if options else None
        return None

    def __str__(self):
        return self.question_text

class TeachingPlan(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='teaching_plans')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title

class Assessment(models.Model):
    title = models.CharField(max_length=200)
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='assessments')
    questions = models.ManyToManyField(Question)
    created_at = models.DateTimeField(auto_now_add=True)
    is_published = models.BooleanField(default=False)

    def __str__(self):
        return self.title

class StudentResponse(models.Model):
    student = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='responses')
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='responses')
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    answer_text = models.TextField()
    is_correct = models.BooleanField()
    submitted_at = models.DateTimeField(auto_now_add=True)
    is_mastered = models.BooleanField(default=False)  # 添加错题掌握状态字段
    explanation = models.TextField(blank=True, null=True)  # 添加AI解析字段

    @property
    def answer(self):
        """为了兼容性，提供answer属性"""
        return self.answer_text

    def __str__(self):
        return f"{self.student.username}'s response to {self.question.id} in {self.assessment.title}"

class Courseware(models.Model):
    title = models.CharField(max_length=200, default='Untitled')
    description = models.TextField(blank=True, default='')
    file = models.FileField(upload_to='courseware/')
    upload_date = models.DateTimeField(default=timezone.now)
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    subject = models.CharField(max_length=100, blank=True, default='')

    def __str__(self):
        return self.title

class AssessmentQuestion(models.Model):
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE)
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    order = models.PositiveIntegerField()

    class Meta:
        ordering = ['order']
        unique_together = ('assessment', 'question')
