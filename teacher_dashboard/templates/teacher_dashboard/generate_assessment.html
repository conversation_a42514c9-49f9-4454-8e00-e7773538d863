<!DOCTYPE html>
<html>
<head>
    <title>考核内容生成</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            color: #343a40;
            padding: 20px;
        }
        .container-fluid {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .question-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: box-shadow 0.3s ease-in-out;
        }
        .question-card:hover {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h2 class="mb-4">考核内容生成</h2>
        <p class="lead">您可以在这里生成考核内容，并将其添加至您的题库。</p>

        <div class="card mt-4">
            <div class="card-header">第一步：设置生成要求</div>
            <div class="card-body">
                <div id="question-request-container">
                    <!-- 初始题目请求 -->
                    <div class="row g-3 mb-2 align-items-center question-request-row">
                        <div class="col-md-5">
                            <label class="form-label">题目类型</label>
                            <select class="form-select">
                                <option>选择题</option>
                                <option>简答题</option>
                                <option>编程题</option>
                                <option>判断题</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">题目数量</label>
                            <input type="number" class="form-control" value="1" min="1">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button class="btn btn-danger" onclick="removeRow(this)">移除</button>
                        </div>
                    </div>
                </div>
                <button class="btn btn-outline-primary mt-2" id="add-question-type-btn">
                    <i class="fas fa-plus me-2"></i>添加其他类型
                </button>
                <hr>
                <div class="mb-3">
                    <label for="keyPointsTextarea" class="form-label">第二步：输入考点（或使用备课教案）</label>
                    <textarea class="form-control" rows="4" id="keyPointsTextarea" placeholder="请输入考点（关键词，如：导数、积分）。如果您在“备课与设计”页面上传了教案，系统将优先使用教案内容。"></textarea>
                </div>
                <button class="btn btn-primary w-100" id="generateAssessmentBtn">
                    <i class="fas fa-cogs me-2"></i>生成试题
                </button>
            </div>
        </div>

        <div id="loadingIndicator" class="text-center mt-4" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">AI正在努力出题中，请稍候...</p>
        </div>

        <div id="assessmentResult" class="mt-4">
            <!-- 生成的题目将在这里展示 -->
        </div>

        <div id="addToBankSection" class="mt-3" style="display: none;">
            <button class="btn btn-success w-100" id="addToBankBtn">
                <i class="fas fa-plus-circle me-2"></i>将选中的题目加入题库
            </button>
        </div>
    </div>

    <div id="toast-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局存储生成的题目数据
        let generatedQuestionsData = [];

        document.getElementById('add-question-type-btn').addEventListener('click', () => {
            const container = document.getElementById('question-request-container');
            const newRow = container.firstElementChild.cloneNode(true);
            newRow.querySelector('select').selectedIndex = 0;
            newRow.querySelector('input').value = 1;
            container.appendChild(newRow);
        });

        function removeRow(button) {
            const rows = document.querySelectorAll('.question-request-row');
            if (rows.length > 1) {
                button.closest('.question-request-row').remove();
            } else {
                showToast('至少需要一个题目类型。', 'warning');
            }
        }

        document.getElementById('generateAssessmentBtn').addEventListener('click', async () => {
            const keyPoints = document.getElementById('keyPointsTextarea').value;
            const assessmentResultDiv = document.getElementById('assessmentResult');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const addToBankSection = document.getElementById('addToBankSection');

            const questionRequests = [];
            document.querySelectorAll('.question-request-row').forEach(row => {
                const type = row.querySelector('select').value;
                const count = row.querySelector('input').value;
                if (count > 0) {
                    questionRequests.push({ type, count });
                }
            });

            if (questionRequests.length === 0) {
                showToast('请至少设置一个题目请求。', 'danger');
                return;
            }

            loadingIndicator.style.display = 'block';
            assessmentResultDiv.innerHTML = '';
            addToBankSection.style.display = 'none';
            generatedQuestionsData = [];

            try {
                const response = await fetch('{% url "teacher_dashboard:generate_assessment" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: new URLSearchParams({
                        'key_points': keyPoints,
                        'question_requests': JSON.stringify(questionRequests)
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    if (data.generated_questions && data.generated_questions.length > 0) {
                        generatedQuestionsData = data.generated_questions;
                        assessmentResultDiv.innerHTML = renderQuestions(generatedQuestionsData);
                        addToBankSection.style.display = 'block';
                        showToast(data.message || '题目生成成功！', 'success');
                    } else {
                        showToast(data.message || '未能生成任何题目。', 'warning');
                    }
                } else {
                    throw new Error(data.message || '服务器返回错误。');
                }
            } catch (error) {
                console.error('Error:', error);
                showToast(`请求失败：${error.message}`, 'danger');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        });

        function renderQuestions(questions) {
            return questions.map((q, index) => `
                <div class="card question-card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <strong>${q.type}</strong>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="${index}" id="q-checkbox-${index}" checked>
                            <label class="form-check-label" for="q-checkbox-${index}">
                                加入题库
                            </label>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text"><strong>题目：</strong> ${q.question.replace(/\n/g, '<br>')}</p>
                        ${q.options && q.options.length > 0 ? `<p class="card-text"><strong>选项：</strong> ${q.options.join('<br>')}</p>` : ''}
                        <p class="card-text"><strong>答案：</strong> ${q.answer.replace(/\n/g, '<br>')}</p>
                    </div>
                </div>
            `).join('');
        }
        
        document.getElementById('addToBankBtn').addEventListener('click', async () => {
            const selectedIndexes = Array.from(document.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
            const questionsToAdd = selectedIndexes.map(index => generatedQuestionsData[parseInt(index, 10)]);

            if (questionsToAdd.length === 0) {
                showToast('请至少选择一道题目加入题库。', 'warning');
                return;
            }

            try {
                const response = await fetch('{% url "add_to_question_bank" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({ questions: questionsToAdd })
                });

                const data = await response.json();
                
                if (response.ok && data.status === 'success') {
                    showToast(data.message, 'success');
                } else {
                    throw new Error(data.message || '添加到题库失败');
                }
            } catch (error) {
                console.error('Error adding to bank:', error);
                showToast(`操作失败：${error.message}`, 'danger');
            }
        });


        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }
    </script>
</body>
</html> 