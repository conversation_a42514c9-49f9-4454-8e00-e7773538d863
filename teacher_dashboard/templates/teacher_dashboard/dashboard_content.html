<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ page_title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .stat-card {
            border-left-width: 5px;
        }
        .border-primary { border-left-color: #0d6efd !important; }
        .border-success { border-left-color: #198754 !important; }
        .border-info { border-left-color: #0dcaf0 !important; }
        .border-warning { border-left-color: #ffc107 !important; }
    </style>
</head>
<body>
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ page_title }}</h1>

    <!-- 综合数据统计 -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">题库题目总数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ question_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">已发布考核数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ assessment_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-poll fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">覆盖学生数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ student_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">总体学习掌握度</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_mastery }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">近期考核正确率趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="accuracyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">题库题型分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4" style="height: 300px;">
                        <canvas id="questionTypeDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 高频错题列表 -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">高频错题 Top 5</h6>
                </div>
                <div class="card-body">
                    {% if top_wrong_questions %}
                        <ul class="list-group">
                            {% for item in top_wrong_questions %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ item.question__question_text|truncatewords:20 }}
                                    <span class="badge bg-danger rounded-pill">{{ item.error_count }} 次错误</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>暂无错题数据。</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Since content is loaded via AJAX, we need to make sure this script runs
// every time new content is loaded. A simple way is to check if the chart
// already exists. If you are using a framework, you'd hook into its lifecycle methods.
// For now, we assume this template is loaded fresh or the container is cleared.

console.log('=== Dashboard Chart Debug Info ===');
console.log('Assessment accuracy labels JSON:', '{{ assessment_accuracy_labels_json|safe }}');
console.log('Assessment accuracy data JSON:', '{{ assessment_accuracy_data_json|safe }}');
console.log('Question type data JSON:', '{{ question_type_data_json|safe }}');
console.log('===================================');

// Helper function to destroy existing chart
function destroyChart(chartId) {
    let chart = Chart.getChart(chartId);
    if (chart) {
        chart.destroy();
    }
}

// 1. Accuracy Trend Chart
destroyChart('accuracyTrendChart');
var ctx1 = document.getElementById("accuracyTrendChart").getContext('2d');

// Parse data with error handling
let accuracyLabels, accuracyData;
try {
    accuracyLabels = JSON.parse('{{ assessment_accuracy_labels_json|safe }}');
    accuracyData = JSON.parse('{{ assessment_accuracy_data_json|safe }}');
} catch (e) {
    console.error('Error parsing accuracy chart data:', e);
    accuracyLabels = ['暂无数据'];
    accuracyData = [0];
}

var accuracyTrendChart = new Chart(ctx1, {
    type: 'line',
    data: {
        labels: accuracyLabels,
        datasets: [{
            label: "正确率 (%)",
            data: accuracyData,
            backgroundColor: 'rgba(78, 115, 223, 0.05)',
            borderColor: 'rgba(78, 115, 223, 1)',
            borderWidth: 2,
            pointBackgroundColor: 'rgba(78, 115, 223, 1)',
            pointRadius: 3,
            pointHitRadius: 10,
        }]
    },
    options: {
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y + '%';
                    }
                }
            }
        }
    }
});


// 2. Question Type Distribution Chart
destroyChart('questionTypeDistributionChart');
var ctx2 = document.getElementById("questionTypeDistributionChart").getContext('2d');

// Parse data with error handling
let questionTypeData, labels, data;
try {
    questionTypeData = JSON.parse('{{ question_type_data_json|safe }}');
    labels = Object.keys(questionTypeData);
    data = Object.values(questionTypeData);

    // If all values are 0, show a message
    if (data.every(val => val === 0)) {
        labels = ['暂无题目数据'];
        data = [1];
    }
} catch (e) {
    console.error('Error parsing question type chart data:', e);
    labels = ['暂无数据'];
    data = [1];
}

var questionTypeDistributionChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: labels,
        datasets: [{
            data: data,
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#dda20a', '#d3342d', '#6c6e7e'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }]
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.label || '';
                        let value = context.raw;

                        // Handle the "no data" case
                        if (label === '暂无题目数据' || label === '暂无数据') {
                            return label;
                        }

                        let total = context.chart.getDatasetMeta(0).total;
                        let percentage = total > 0 ? (value / total * 100).toFixed(2) + '%' : '0%';
                        return label + ': ' + value + ' (' + percentage + ')';
                    }
                }
            }
        }
    }
});

</script>

</body>
</html> 