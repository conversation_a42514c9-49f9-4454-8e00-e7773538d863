<!DOCTYPE html>
<html>
<head>
    <title>教师端功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .feature-card {
            transition: transform 0.2s;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .demo-section {
            margin-bottom: 3rem;
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-chalkboard-teacher me-3"></i>
                        AI教学实训模型系统 - 教师端
                    </h1>
                    <p class="lead text-muted">智能化教学工具，提升教学效率与质量</p>
                </div>
            </div>
        </div>

        <!-- 功能概览 -->
        <div class="demo-section">
            <h2 class="text-center mb-4">
                <i class="fas fa-star text-warning me-2"></i>核心功能
            </h2>
            <div class="row g-4">
                <!-- 备课设计 -->
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card border-primary position-relative">
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check"></i> 已实现
                        </span>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-file-alt fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">备课设计</h5>
                            <p class="card-text">AI辅助教案生成，智能备课建议，教学资源整合</p>
                            <a href="{% url 'teacher_dashboard:prepare_design' %}" 
                               class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>体验功能
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 题库管理 -->
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card border-success position-relative">
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check"></i> 已实现
                        </span>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-database fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">题库管理</h5>
                            <p class="card-text">智能题目生成，题库分类管理，题目质量评估</p>
                            <a href="{% url 'teacher_dashboard:question_bank' %}" 
                               class="btn btn-success" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>体验功能
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 考核发布 -->
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card border-warning position-relative">
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check"></i> 已实现
                        </span>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-clipboard-check fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">考核发布</h5>
                            <p class="card-text">在线考试创建，考核任务发布，考试时间管理</p>
                            <a href="{% url 'teacher_dashboard:assessment_management' %}" 
                               class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>体验功能
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 数据分析 -->
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card border-info position-relative">
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check"></i> 已实现
                        </span>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-chart-bar fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">数据分析</h5>
                            <p class="card-text">学生成绩统计，学习进度跟踪，教学效果评估</p>
                            <a href="{% url 'teacher_dashboard:data_analysis' %}" 
                               class="btn btn-info" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>体验功能
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI智能特色 -->
        <div class="demo-section">
            <h2 class="text-center mb-4">
                <i class="fas fa-magic text-primary me-2"></i>AI智能特色
            </h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-robot fa-2x text-primary mb-3"></i>
                            <h5>智能教案生成</h5>
                            <p class="text-muted">基于教学文档自动生成结构化教案，提供个性化教学建议</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-2x text-success mb-3"></i>
                            <h5>自动题目生成</h5>
                            <p class="text-muted">AI根据教学内容自动生成多样化题目，丰富题库资源</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-analytics fa-2x text-warning mb-3"></i>
                            <h5>智能数据分析</h5>
                            <p class="text-muted">深度分析学生学习数据，提供教学改进建议</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用指南 -->
        <div class="demo-section">
            <h2 class="text-center mb-4">
                <i class="fas fa-compass text-info me-2"></i>使用指南
            </h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chalkboard-teacher text-primary me-2"></i>教师操作流程</h6>
                                    <ol class="list-group list-group-flush">
                                        <li class="list-group-item border-0 px-0">1. 上传教学文档生成教案</li>
                                        <li class="list-group-item border-0 px-0">2. 创建和管理题库</li>
                                        <li class="list-group-item border-0 px-0">3. 发布考核任务</li>
                                        <li class="list-group-item border-0 px-0">4. 分析学生学习数据</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-lightbulb text-warning me-2"></i>教学建议</h6>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item border-0 px-0">• 定期更新教学内容</li>
                                        <li class="list-group-item border-0 px-0">• 关注学生学习进度</li>
                                        <li class="list-group-item border-0 px-0">• 利用AI生成多样化题目</li>
                                        <li class="list-group-item border-0 px-0">• 根据数据调整教学策略</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <a href="{% url 'teacher_dashboard:dashboard' %}" class="btn btn-lg btn-primary">
                <i class="fas fa-home me-2"></i>返回教师仪表板
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
