<!DOCTYPE html>
<html>
<head>
    <title>学情数据分析</title>
    <!-- 引入 Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入 Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Aria<PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #f8f9fa;
            color: #343a40;
            padding: 20px;
        }
        .container-fluid {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            color: #fff;
            margin-bottom: 20px;
        }
        .stat-card h4 {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .stat-card p {
            font-size: 1rem;
            margin-bottom: 0;
        }
        .bg-primary-light { background-color: #5867dd; }
        .bg-success-light { background-color: #34bfa3; }
        .bg-warning-light { background-color: #ffb822; }
        .bg-info-light { background-color: #36a3f7; }
        .table-responsive {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h2 class="mb-4"><i class="fas fa-chart-line me-2"></i>学情数据分析</h2>
        <p class="lead mb-4">欢迎，{{ user.username }}！这是基于所有历史数据的综合学情分析报告。</p>

        <!-- 总体数据概览 -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card bg-primary-light">
                    <h4>{{ total_student_count }}</h4>
                    <p>学生总数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success-light">
                    <h4>{{ total_assessment_count }}</h4>
                    <p>已发布考核数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning-light">
                    <h4>{{ participating_student_count }}</h4>
                    <p>参与考核学生数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info-light">
                    <h4>{{ overall_avg_accuracy }}%</h4>
                    <p>总体平均正确率</p>
                </div>
            </div>
        </div>

        <!-- 近期考核分析 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-file-alt me-2"></i>近期考核分析 (最近10次)</h5>
            </div>
            <div class="card-body">
                {% if assessments_summary %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>考核标题</th>
                                    <th>提交人数</th>
                                    <th>平均分</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for summary in assessments_summary %}
                                <tr>
                                    <td>{{ summary.assessment.title }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span>{{ summary.submitted_count }}/{{ summary.total_students }}</span>
                                            <div class="progress ms-2" style="width: 100px; height: 10px;">
                                                <div class="progress-bar" role="progressbar" style="width: {% widthratio summary.submitted_count summary.total_students 100 %}%;" aria-valuenow="{% widthratio summary.submitted_count summary.total_students 100 %}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="fw-bold">{{ summary.average_score }}</span>
                                            <div class="progress ms-2" style="width: 100px; height: 10px;">
                                                <div class="progress-bar 
                                                    {% if summary.average_score >= 85 %}bg-success{% elif summary.average_score >= 60 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                    role="progressbar" style="width: {{ summary.average_score }}%;" aria-valuenow="{{ summary.average_score }}" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{% url 'teacher_dashboard:assessment_analysis' summary.assessment.id %}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-light text-center" role="alert">
                        暂无已发布的考核。
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 学生表现排行 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star me-2"></i>学生光荣榜 (Top 5)</h5>
                    </div>
                    <div class="card-body">
                        {% if top_students %}
                            <ul class="list-group list-group-flush">
                                {% for student_perf in top_students %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-user-graduate me-2"></i>{{ student_perf.student.username }}</span>
                                    <span class="badge bg-success rounded-pill">平均正确率: {{ student_perf.avg_accuracy }}%</span>
                                </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <p class="text-center text-muted">暂无学生参与考核。</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-hand-holding-heart me-2"></i>需关注学生 (Bottom 5)</h5>
                    </div>
                    <div class="card-body">
                        {% if bottom_students %}
                            <ul class="list-group list-group-flush">
                                {% for student_perf in bottom_students %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-user me-2"></i>{{ student_perf.student.username }}</span>
                                    <span class="badge bg-danger rounded-pill">平均正确率: {{ student_perf.avg_accuracy }}%</span>
                                </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                             <p class="text-center text-muted">暂无需要特别关注的学生。</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 引入 Bootstrap 5 JS and Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.7/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
</body>
</html> 