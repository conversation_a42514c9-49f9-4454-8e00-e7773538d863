<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<style>
    /* Basic styling for content loaded in iframe */
    body {
        background-color: #f8f9fa;
        padding: 1.5rem;
    }
    .card.shadow {
        box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
    }
</style>

<div class="container-fluid">
    <!-- Page Heading -->
    <h1 class="h3 mb-4 text-gray-800">课件资源管理</h1>

    <!-- Upload Form Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">上传新课件</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="title">标题</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="例如：高一数学上册第一单元" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="subject">学科</label>
                        <input type="text" class="form-control" id="subject" name="subject" placeholder="例如：数学" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="description">描述</label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="简要描述课件内容..."></textarea>
                </div>
                <div class="form-group mt-3">
                    <label for="file">选择文件</label>
                    <input type="file" class="form-control-file" id="file" name="file" required>
                </div>
                <button type="submit" class="btn btn-primary mt-3">
                    <i class="fas fa-upload fa-sm text-white-50"></i> 上传
                </button>
            </form>
        </div>
    </div>

    <!-- Courseware List Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">我的课件库</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>标题</th>
                            <th>学科</th>
                            <th>描述</th>
                            <th>上传日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in coursewares %}
                        <tr>
                            <td>{{ item.title }}</td>
                            <td>{{ item.subject }}</td>
                            <td>{{ item.description|default:'-' }}</td>
                            <td>{{ item.upload_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{{ item.file.url }}" class="btn btn-sm btn-info" target="_blank">
                                    <i class="fas fa-download fa-sm"></i> 查看/下载
                                </a>
                                <!-- 未来可在此处添加编辑和删除按钮 -->
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">您还没有上传任何课件。</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div> 