<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<style>
    /* Basic styling for content loaded in iframe */
    body {
        background-color: #f8f9fa;
        padding: 1.5rem;
    }
    .card.shadow {
        box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
    }
</style>

<div class="container-fluid">
    <!-- Page Heading -->
    <h1 class="h3 mb-4 text-gray-800">课件资源管理</h1>

    <!-- Upload Form Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">上传新课件</h6>
        </div>
        <div class="card-body">
            <form id="courseware-upload-form" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="title">标题</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="例如：高一数学上册第一单元" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="subject">学科</label>
                        <input type="text" class="form-control" id="subject" name="subject" placeholder="例如：数学" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="description">描述</label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="简要描述课件内容..."></textarea>
                </div>
                <div class="form-group mt-3">
                    <label for="file">选择文件</label>
                    <input type="file" class="form-control-file" id="file" name="file" required>
                </div>
                <button type="submit" class="btn btn-primary mt-3" id="upload-btn">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                    <i class="fas fa-upload fa-sm text-white-50"></i>
                    <span class="btn-text">上传</span>
                </button>
            </form>

            <!-- Success/Error Messages -->
            <div id="upload-message" class="mt-3" style="display: none;"></div>
        </div>
    </div>

    <!-- Courseware List Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">我的课件库</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="coursewares-table" class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>标题</th>
                            <th>学科</th>
                            <th>描述</th>
                            <th>上传日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in coursewares %}
                        <tr>
                            <td>{{ item.title }}</td>
                            <td>{{ item.subject }}</td>
                            <td>{{ item.description|default:'-' }}</td>
                            <td>{{ item.upload_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{{ item.file.url }}" class="btn btn-sm btn-info" target="_blank">
                                    <i class="fas fa-download fa-sm"></i> 查看/下载
                                </a>
                                <!-- 未来可在此处添加编辑和删除按钮 -->
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">您还没有上传任何课件。</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('courseware-upload-form');
    const uploadBtn = document.getElementById('upload-btn');
    const messageDiv = document.getElementById('upload-message');
    const spinner = uploadBtn.querySelector('.spinner-border');
    const btnText = uploadBtn.querySelector('.btn-text');

    if (uploadForm) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Show loading state
            uploadBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '上传中...';
            messageDiv.style.display = 'none';

            try {
                const formData = new FormData(uploadForm);

                const response = await fetch(uploadForm.action || window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                    }
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // Show success message
                    messageDiv.className = 'alert alert-success mt-3';
                    messageDiv.textContent = result.message;
                    messageDiv.style.display = 'block';

                    // Reset form
                    uploadForm.reset();

                    // Add new row to table
                    addCoursewareToTable(result.courseware);

                    // Scroll to success message
                    messageDiv.scrollIntoView({ behavior: 'smooth' });

                } else {
                    // Show error message
                    messageDiv.className = 'alert alert-danger mt-3';
                    messageDiv.textContent = result.message || '上传失败，请重试';
                    messageDiv.style.display = 'block';
                }

            } catch (error) {
                console.error('Upload error:', error);
                messageDiv.className = 'alert alert-danger mt-3';
                messageDiv.textContent = '网络错误，请检查连接后重试';
                messageDiv.style.display = 'block';
            } finally {
                // Reset button state
                uploadBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '上传';
            }
        });
    }

    function addCoursewareToTable(courseware) {
        const tbody = document.querySelector('#coursewares-table tbody');
        if (!tbody) return;

        // Remove "no data" row if it exists
        const noDataRow = tbody.querySelector('tr td[colspan="5"]');
        if (noDataRow) {
            noDataRow.parentElement.remove();
        }

        // Create new row
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>${courseware.title}</td>
            <td>${courseware.subject}</td>
            <td>${courseware.description}</td>
            <td>${courseware.upload_date}</td>
            <td>
                <a href="#" class="btn btn-sm btn-info" target="_blank">
                    <i class="fas fa-download fa-sm"></i> 查看/下载
                </a>
            </td>
        `;

        // Insert at the beginning of the table
        tbody.insertBefore(newRow, tbody.firstChild);

        // Highlight the new row briefly
        newRow.style.backgroundColor = '#d4edda';
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);
    }
});
</script>