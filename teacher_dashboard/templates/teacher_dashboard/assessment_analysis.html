{% extends "base/dashboard_base.html" %}

{% block title %}考情分析: {{ assessment.title }}{% endblock %}

{% block extra_css %}
<style>
    .stat-card { text-align: center; }
    .student-list { max-height: 200px; overflow-y: auto; }
</style>
{% endblock %}

{% block initial_content %}
<div class="container-fluid">
    <h2 class="mb-4">考情分析: {{ assessment.title }}</h2>
    
    <!-- 概览数据 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <h5 class="card-title">提交情况</h5>
                    <p class="display-4">{{ submitted_students.count }}/{{ unsubmitted_students.count|add:submitted_students.count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <h5 class="card-title">整体正确率</h5>
                    <p class="display-4">{{ overall_accuracy|floatformat:2 }}%</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <h6 class="card-subtitle mb-2 text-muted">高频错题</h6>
                    {% if top_wrong_questions %}
                        <ul class="list-group list-group-flush text-start">
                            {% for stat in top_wrong_questions %}
                                <li class="list-group-item">
                                    Q{{ forloop.counter }}: {{ stat.question.question_text|truncatechars:50 }}
                                    <span class="badge bg-danger float-end">{{ stat.accuracy|floatformat:2 }}%</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="fs-5">暂无高频错题</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据切换标签 -->
    <ul class="nav nav-tabs mt-4" id="analysisTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats-content" type="button" role="tab" aria-controls="stats-content" aria-selected="true">题目统计</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details-content" type="button" role="tab" aria-controls="details-content" aria-selected="false">学生详情</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status-content" type="button" role="tab" aria-controls="status-content" aria-selected="false">提交状态</button>
        </li>
    </ul>

    <div class="tab-content" id="analysisTabContent">
        <!-- 题目统计内容 -->
        <div class="tab-pane fade show active" id="stats-content" role="tabpanel" aria-labelledby="stats-tab">
            <div class="card card-body">
                <canvas id="accuracyChart"></canvas>
            </div>
        </div>

        <!-- 学生详情内容 -->
        <div class="tab-pane fade" id="details-content" role="tabpanel" aria-labelledby="details-tab">
            <div class="accordion" id="studentDetailsAccordion">
                {% for detail in student_details %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading-{{ forloop.counter }}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{{ forloop.counter }}" aria-expanded="false" aria-controls="collapse-{{ forloop.counter }}">
                            <div class="w-100 d-flex justify-content-between align-items-center pe-2">
                                <span>
                                    <i class="fas fa-user me-2"></i>{{ detail.student.username }}
                                </span>
                                {% if detail.has_submitted %}
                                    <span class="badge {% if detail.score >= 80 %}bg-success{% elif detail.score >= 60 %}bg-warning{% else %}bg-danger{% endif %}">
                                        得分: {{ detail.score|floatformat:2 }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">未提交</span>
                                {% endif %}
                            </div>
                        </button>
                    </h2>
                    <div id="collapse-{{ forloop.counter }}" class="accordion-collapse collapse" aria-labelledby="heading-{{ forloop.counter }}" data-bs-parent="#studentDetailsAccordion">
                        <div class="accordion-body">
                            {% if detail.has_submitted %}
                            <ul class="list-group">
                                {% for r_detail in detail.responses_details %}
                                <li class="list-group-item">
                                    <p class="mb-1"><strong>题目 {{ forloop.counter }}: </strong>{{ r_detail.question.question_text }}</p>
                                    <p class="mb-1"><strong>学生答案: </strong>
                                        <span class="badge {% if r_detail.is_correct %}bg-success-light text-dark{% else %}bg-danger-light text-dark{% endif %}">
                                            {{ r_detail.student_answer|default:"未作答" }}
                                        </span>
                                    </p>
                                    {% if not r_detail.is_correct %}
                                    <p class="mb-0"><strong>正确答案: </strong>
                                        <span class="badge bg-info-light text-dark">{{ r_detail.question.answer }}</span>
                                    </p>
                                    {% endif %}
                                </li>
                                {% endfor %}
                            </ul>
                            {% else %}
                            <p class="text-muted text-center">该学生未提交本次考核。</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 提交状态内容 -->
        <div class="tab-pane fade" id="status-content" role="tabpanel" aria-labelledby="status-tab">
            <div class="card card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>已提交 ({{ submitted_students.count }})</h5>
                        <ul class="list-group">
                            {% for detail in student_details %}
                                {% if detail.has_submitted %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ detail.student.username }}
                                    <span class="text-muted" style="font-size: 0.8em;">
                                        提交时间: {% if detail.submission_time %}{{ detail.submission_time|date:"Y-m-d H:i" }}{% else %}未知{% endif %}
                                    </span>
                                </li>
                                {% endif %}
                            {% empty %}
                            <li class="list-group-item text-muted">暂无学生提交</li>
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>未提交 ({{ unsubmitted_students.count }})</h5>
                        <ul class="list-group">
                            {% for student in unsubmitted_students %}
                            <li class="list-group-item">{{ student.username }}</li>
                            {% empty %}
                            <li class="list-group-item text-muted">所有学生都已提交</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a href="{% url 'teacher_dashboard:assessment_list' %}" class="btn btn-secondary">返回考核列表</a>
    </div>
</div>
{% endblock %}

{% block modals %}{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('accuracyChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [{% for stat in question_stats %}"题目 {{ forloop.counter }}",{% endfor %}],
                datasets: [{
                    label: '正确率 (%)',
                    data: [{% for stat in question_stats %}{{ stat.accuracy }},{% endfor %}],
                    backgroundColor: 'rgba(25, 135, 84, 0.7)'
                }]
            },
            options: { scales: { y: { beginAtZero: true, max: 100 } } }
        });
    });
</script>
{% endblock %} 