<!DOCTYPE html>
<html>
<head>
    <title>题库管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            color: #343a40;
            padding: 20px;
        }
        .container-fluid {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }
        .question-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }
        .question-item:last-child {
            border-bottom: none;
        }
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-university me-2"></i> 题库管理</h2>
            <div>
                <button class="btn btn-primary" id="publish-assessment-btn" disabled>
                    <i class="fas fa-paper-plane me-2"></i> 发布为考核
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="select-all-checkbox">
                    <label class="form-check-label" for="select-all-checkbox">
                        全选/取消全选
                    </label>
                </div>
            </div>
            <div class="card-body" id="questions-list-container">
                {% if page_obj.object_list %}
                    {% for question in page_obj.object_list %}
                        <div class="question-item" data-question-id="{{ question.id }}">
                            <div class="d-flex justify-content-between">
                                <div class="flex-grow-1 d-flex">
                                    <div class="me-3">
                                        <input class="form-check-input question-checkbox" type="checkbox" value="{{ question.id }}">
                                    </div>
                                    <div>
                                        <h6 class="mb-1"><strong>【{{ question.get_question_type_display }}】</strong></h6>
                                        <div class="question-text">{{ question.question_text|linebreaksbr }}</div>
                                        <p class="mt-2"><strong>答案：</strong> <span class="answer-text">{{ question.correct_answer|linebreaksbr }}</span></p>
                                    </div>
                                </div>
                                <div class="flex-shrink-0 ms-3">
                                    <button class="btn btn-sm btn-outline-primary edit-btn">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-btn">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-center text-muted">您的题库中还没有题目。</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mb-0">
                            {% if page_obj.has_previous %}
                                <li class="page-item"><a class="page-link" href="?page=1">&laquo; 首页</a></li>
                                <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a></li>
                            {% endif %}
                            
                            <li class="page-item active" aria-current="page">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a></li>
                                <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">尾页 &raquo;</a></li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Edit Question Modal -->
    <div class="modal fade" id="editQuestionModal" tabindex="-1" aria-labelledby="editQuestionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editQuestionModalLabel">编辑题目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-question-form">
                        <input type="hidden" id="edit-question-id">
                        <div class="mb-3">
                            <label for="edit-question-text" class="form-label">题目内容</label>
                            <textarea class="form-control" id="edit-question-text" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit-correct-answer" class="form-label">正确答案</label>
                            <textarea class="form-control" id="edit-correct-answer" rows="2" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-changes-btn">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <div id="toast-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 