<div class="container-fluid py-4">
    <h2 class="mb-4">备课与设计</h2>
    <p class="lead mb-4">您可以在这里上传教学资料，由AI辅助您编写教案。</p>

    <!-- Upload Card -->
    <div class="card">
        <div class="card-header">
            上传教学资料
        </div>
        <div class="card-body">
            <form id="upload-form" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="teaching-document-input" class="form-label">支持 .txt, .pdf, .docx 格式的文件：</label>
                    <input class="form-control" type="file" id="teaching-document-input" name="teaching_document" multiple required>
                </div>
                <button type="submit" class="btn btn-primary" id="generate-btn">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                    <span class="button-text">上传并生成教案</span>
                </button>
            </form>
            <div id="error-message-area" class="alert alert-danger mt-3" style="display: none;"></div>
        </div>
    </div>

    <!-- AI Generated Content Card -->
    <div class="card mt-4">
        <div class="card-header">
            AI 辅助生成教案
        </div>
        <div class="card-body">
            <div id="ai-results-area" class="bg-light p-3 rounded" style="min-height: 200px;">
                <p class="text-muted">AI 生成的教案将显示在这里。</p>
            </div>
        </div>
        <div class="card-footer text-center bg-light border-top-0 pt-3">
            <p class="text-muted small mb-2">第一步：处理上方AI内容</p>
            <button type="button" id="save-plan-btn" class="btn btn-primary">
                <i class="fas fa-save fa-sm"></i> 保存教案
            </button>
            <button type="button" id="export-btn" class="btn btn-secondary">
                <i class="fas fa-download fa-sm"></i> 导出为Word文档
            </button>
        </div>
    </div>

    <!-- New Question Generation Section (initially hidden) -->
    <div id="generate-questions-section" class="card mt-4" style="display: none;">
        <div class="card-header">
            第二步：为已保存的教案生成题目
        </div>
        <div class="card-body">
            <form id="generate-questions-form" data-plan-id="">
                <p class="mb-3">请选择要生成的题型和数量：</p>
                <div class="row">
                    <!-- Multiple Choice Card -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="form-check d-flex justify-content-between">
                                    <input class="form-check-input" type="checkbox" value="MC" id="check-mc" data-question-type="MC" checked>
                                    <label class="form-check-label fw-bold" for="check-mc">
                                        选择题
                                    </label>
                                </div>
                                <hr class="my-2">
                                <label for="count-mc" class="form-label small">数量:</label>
                                <input type="number" class="form-control form-control-sm" value="3" min="1" max="10" id="count-mc" data-question-count="MC">
                            </div>
                        </div>
                    </div>
                    <!-- Short Answer Card -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="form-check d-flex justify-content-between">
                                    <input class="form-check-input" type="checkbox" value="SA" id="check-sa" data-question-type="SA">
                                    <label class="form-check-label fw-bold" for="check-sa">
                                        简答题
                                    </label>
                                </div>
                                <hr class="my-2">
                                <label for="count-sa" class="form-label small">数量:</label>
                                <input type="number" class="form-control form-control-sm" value="2" min="1" max="5" id="count-sa" data-question-count="SA" disabled>
                            </div>
                        </div>
                    </div>
                    <!-- Programming Question Card -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="form-check d-flex justify-content-between">
                                    <input class="form-check-input" type="checkbox" value="PG" id="check-pg" data-question-type="PG">
                                    <label class="form-check-label fw-bold" for="check-pg">
                                        编程题
                                    </label>
                                </div>
                                <hr class="my-2">
                                <label for="count-pg" class="form-label small">数量:</label>
                                <input type="number" class="form-control form-control-sm" value="1" min="1" max="5" id="count-pg" data-question-count="PG" disabled>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="submit" id="generate-questions-btn" class="btn btn-success mt-3">
                    <span class="spinner-border spinner-border-sm" style="display: none;" role="status" aria-hidden="true"></span>
                    <i class="fas fa-cogs me-1"></i>
                    <span class="button-text">开始生成题目</span>
                </button>
            </form>
            
            <!-- This is the container where the dynamically generated question cards will be injected. -->
            <div id="generated-questions-container" class="mt-4"></div>

        </div>
    </div>

    <!-- Divider -->
    <hr class="my-4">

    <div class="container-fluid py-4">
        <!-- Teaching Plan List Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">我的教案库</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>创建日期</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for plan in teaching_plans %}
                            <tr>
                                <td>{{ plan.title }}</td>
                                <td>{{ plan.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <a href="{% url 'teacher_dashboard:prepare_design' %}?plan_id={{ plan.id }}#results-section" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye fa-sm"></i> 查看详情
                                    </a>
                                    <form method="post" action="{% url 'teacher_dashboard:generate_questions' plan.id %}" style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-success">
                                            <i class="fas fa-cogs fa-sm"></i> 生成题目
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% empty %}
                            <tr class="empty-row">
                                <td colspan="3" class="text-center">您还没有创建任何教案。</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {% if selected_plan %}
        <!-- Generated Questions Card -->
        <div class="card shadow mb-4" id="results-section" {% if not selected_plan %}style="display: none;"{% endif %}>
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary plan-title">教案详情与生成结果：{{ selected_plan.title|default:"待选择" }}</h6>
            </div>
            <div class="card-body">
                <h5>教案内容</h5>
                <div class="p-3 bg-light rounded mb-3">
                    {{ selected_plan.content|linebreaksbr|default:"请先在上方生成或在下方选择一个教案。" }}
                </div>
                
                <hr>
                
                <h5>基于该教案生成的题目</h5>
                <div id="existing-questions-list">
                    {% if generated_questions %}
                        {% for question in generated_questions %}
                            <div class="card mb-2">
                                <div class="card-body">
                                    <strong>{{ question.get_question_type_display }}:</strong> {{ question.question_text }}
                                    <br>
                                    <small><strong>答案:</strong> {{ question.correct_answer }}</small>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">此教案下暂无题目。</p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

<script>
</script> 