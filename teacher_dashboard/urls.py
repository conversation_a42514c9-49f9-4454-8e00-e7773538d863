from django.urls import path
from . import views

app_name = 'teacher_dashboard'

urlpatterns = [
    # Page-rendering URLs
    path('', views.dashboard, name='dashboard'),
    path('dashboard-content/', views.dashboard_content, name='dashboard_content'),
    path('prepare-design/', views.prepare_design_view, name='prepare_design'),
    path('question-bank/', views.question_bank, name='question_bank'),
    path('assessments/', views.assessment_list_view, name='assessment_list'),
    path('assessments/generate/', views.generate_assessment_view, name='generate_assessment'),
    path('assessments/<int:assessment_id>/analysis/', views.assessment_analysis_view, name='assessment_analysis'),
    path('learning-data-analysis/', views.analyze_learning_data_view, name='analyze_learning_data'),
    path('courseware/', views.courseware_management, name='courseware_management'),

    # API endpoints
    path('api/question/<int:question_id>/', views.question_detail_api, name='question_detail_api'),
    path('api/publish-assessment/', views.publish_assessment_api, name='publish_assessment_api'),
    path('api/save-lesson-plan/', views.save_teaching_plan, name='save_teaching_plan'),
    path('api/export-lesson-plan/', views.export_teaching_plan, name='export_teaching_plan'),
    path('api/plan/<int:plan_id>/generate-questions/', views.generate_questions, name='generate_questions'),
    path('api/add-question-to-bank/', views.add_question_to_bank, name='add_question_to_bank'),
    path('api/save-lesson-plan-as-docx/', views.save_lesson_plan_as_docx, name='save_lesson_plan_as_docx'),
] 