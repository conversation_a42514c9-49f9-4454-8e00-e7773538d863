from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from openai import OpenAI
import json
import os
from io import BytesIO
from .models import Question, Assessment, Courseware, TeachingPlan
import re
from collections import Counter
from users.models import User
from django.db.models import Avg, Count, F, Q
from django.db.models.functions import Cast
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
import docx
import fitz  # PyMuPDF
from docx.shared import Pt
from django.db.models import FloatField
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from student_dashboard.models import AssessmentAttempt, StudentAnswer

# Helper function to get API client
def get_dashscope_client():
    """Initializes and returns the DashScope OpenAI client."""
    api_key = os.getenv('DASHSCOPE_API_KEY') or getattr(settings, 'DASHSCOPE_API_KEY', None)
    if not api_key:
        return None
    return OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

# Helper function to read uploaded files
def read_uploaded_files(files):
    """Reads content from uploaded files (docx, pdf, txt)."""
    document_text = ''
    for f in files:
        try:
            if f.name.endswith('.docx'):
                doc = docx.Document(f)
                for para in doc.paragraphs:
                    document_text += para.text + '\n'
            elif f.name.endswith('.pdf'):
                with fitz.open(stream=f.read(), filetype='pdf') as doc:
                    for page in doc:
                        document_text += page.get_text() + '\n'
            elif f.name.endswith('.txt'):
                document_text += f.read().decode('utf-8') + '\n'
            else:
                return None, f'不支持的文件格式: {f.name}'
        except Exception as e:
            return None, f'读取文件 {f.name} 时出错: {e}'
    return document_text, None


@login_required
def dashboard(request):
    """
    Renders the main dashboard shell. The content is loaded via AJAX.
    """
    if not (request.user.is_authenticated and getattr(request.user, 'is_teacher', False)):
        return redirect('users:login')
    context = {
        'is_dashboard_active': True,
    }
    return render(request, 'teacher_dashboard/dashboard.html', context)


@login_required
def dashboard_content(request):
    """
    Provides the data and renders the HTML for the teacher dashboard content.
    This is loaded via AJAX.
    """
    teacher = request.user
    question_count = Question.objects.count()
    assessment_count = Assessment.objects.filter(teacher=teacher).count()
    
    # Placeholders for other data
    # covered_students_count = 0 
    # overall_mastery = 0

    # Calculate covered students
    covered_students_count = AssessmentAttempt.objects.filter(
        assessment__teacher=teacher
    ).values('student').distinct().count()

    # Calculate overall mastery
    all_teacher_attempts = AssessmentAttempt.objects.filter(assessment__teacher=teacher)
    if all_teacher_attempts.exists():
        total_correct_answers = StudentAnswer.objects.filter(
            assessment_attempt__in=all_teacher_attempts,
            is_correct=True
        ).count()
        
        # Total number of questions answered across all attempts
        total_answered_questions = StudentAnswer.objects.filter(
            assessment_attempt__in=all_teacher_attempts
        ).count()
        
        overall_mastery = (total_correct_answers / total_answered_questions) * 100 if total_answered_questions > 0 else 0
    else:
        overall_mastery = 0


    # 1. Question Type Distribution data
    question_type_distribution = Question.objects.values('question_type').annotate(count=Count('id'))

    # To make it more readable in the template, let's create a dictionary
    # mapping the type code (e.g., 'MC') to its display name and count.
    type_map = dict(Question.QUESTION_TYPE_CHOICES)
    question_type_data = {
        type_map.get(item['question_type'], item['question_type']): item['count']
        for item in question_type_distribution
    }

    # If no data exists, provide sample data for demonstration
    if not question_type_data:
        question_type_data = {
            '选择题': 0,
            '判断题': 0,
            '简答题': 0,
            '编程题': 0
        }

    # 2. Recent Assessment Accuracy Trend
    recent_assessments = Assessment.objects.filter(teacher=teacher, is_published=True).order_by('-created_at')[:5]
    assessment_accuracy_labels = []
    assessment_accuracy_data = []
    
    # We iterate in reverse to show oldest first on the chart
    for assessment in reversed(recent_assessments):
        attempts = AssessmentAttempt.objects.filter(assessment=assessment)
        if attempts.exists():
            correct_answers = StudentAnswer.objects.filter(
                assessment_attempt__in=attempts, 
                is_correct=True
            ).count()
            total_questions_in_assessment = assessment.questions.count()
            total_attempts = attempts.count()
            
            # Avoid division by zero
            if total_questions_in_assessment > 0 and total_attempts > 0:
                # Overall accuracy for this assessment
                accuracy = (correct_answers / (total_questions_in_assessment * total_attempts)) * 100
            else:
                accuracy = 0
        else:
            accuracy = 0 # No attempts, so accuracy is 0
        
        assessment_accuracy_labels.append(assessment.title)
        assessment_accuracy_data.append(round(accuracy, 2))

    # If no assessment data exists, provide sample data for demonstration
    if not assessment_accuracy_labels:
        assessment_accuracy_labels = ['暂无考核数据']
        assessment_accuracy_data = [0]

    # 3. Top 5 Wrong Questions
    wrong_answers = StudentAnswer.objects.filter(
        assessment_attempt__assessment__teacher=teacher,
        is_correct=False
    ).values('question__question_text') \
     .annotate(error_count=Count('question')) \
     .order_by('-error_count')[:5]

    # Debug: Print data to console for troubleshooting
    print("=== DEBUG CHART DATA ===")
    print(f"Question count: {question_count}")
    print(f"Assessment count: {assessment_count}")
    print(f"Question type data: {question_type_data}")
    print(f"Assessment accuracy labels: {assessment_accuracy_labels}")
    print(f"Assessment accuracy data: {assessment_accuracy_data}")
    print(f"Question type data JSON: {json.dumps(question_type_data)}")
    print(f"Assessment accuracy labels JSON: {json.dumps(assessment_accuracy_labels)}")
    print(f"Assessment accuracy data JSON: {json.dumps(assessment_accuracy_data)}")
    print("========================")

    context = {
        'question_count': question_count,
        'assessment_count': assessment_count,
        'covered_students_count': covered_students_count,
        'overall_mastery': f'{round(overall_mastery, 2)}%',
        'question_type_data_json': json.dumps(question_type_data),
        'assessment_accuracy_labels_json': json.dumps(assessment_accuracy_labels),
        'assessment_accuracy_data_json': json.dumps(assessment_accuracy_data),
        'top_wrong_questions': wrong_answers,
        'page_title': '教师后台',
    }
    return render(request, 'teacher_dashboard/dashboard_content.html', context)


@login_required
def prepare_design_view(request):
    """
    Handles the Prepare & Design page.
    """
    if request.method == 'POST':
        files = request.FILES.getlist('teaching_document')
        if not files:
            return JsonResponse({'status': 'error', 'message': '没有上传文件。'}, status=400)

        document_text, error_message = read_uploaded_files(files)
        if error_message:
            return JsonResponse({'status': 'error', 'message': error_message}, status=400)
        
        if document_text is None or not document_text.strip():
            return JsonResponse({'status': 'error', 'message': '无法从文档中提取任何文本内容。'}, status=400)

        client = get_dashscope_client()
        if not client:
            return JsonResponse({'status': 'error', 'message': 'AI服务未配置：找不到API密钥。'}, status=500)

        try:
            prompt = f"""
            作为一名经验丰富的AI教学助手，请根据以下教学材料，为我生成一份结构化、内容详细的教案。

            教案应包含以下几个部分，请使用Markdown格式进行组织：
            1.  **教学目标**：明确说明学生通过本次教学应掌握的知识和技能。
            2.  **教学重点与难点**：清晰地指出本次教学的核心内容和学生可能感到困难的地方。
            3.  **教学准备**：列出需要的教学工具或材料（如课件、软件等）。
            4.  **教学过程**：设计详细的教学步骤，包括导入、新课讲授、互动环节、课堂练习和总结。每个步骤建议有时间分配。

            --- 教学材料 ---
            {document_text}
            --- 教案生成开始 ---
            """
            completion = client.chat.completions.create(
                model="qwen-turbo",
                messages=[{'role': 'user', 'content': prompt}],
                temperature=0.7,
            )
            suggestions = completion.choices[0].message.content
            return JsonResponse({'status': 'success', 'preparation_suggestions': suggestions})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'AI教案生成失败: {str(e)}'}, status=500)

    teacher = request.user
    teaching_plans = TeachingPlan.objects.filter(teacher=teacher).order_by('-created_at')
    
    selected_plan_id = request.GET.get('plan_id')
    selected_plan = None
    generated_questions = None

    if selected_plan_id:
        try:
            selected_plan = TeachingPlan.objects.get(id=selected_plan_id, teacher=teacher)
            generated_questions = selected_plan.generated_questions.all()
        except TeachingPlan.DoesNotExist:
            pass

    context = {
        'page_title': 'AI智能备课',
        'teaching_plans': teaching_plans,
        'selected_plan': selected_plan,
        'generated_questions': generated_questions,
    }
    return render(request, 'teacher_dashboard/prepare_design.html', context)


@login_required
def generate_assessment_view(request):
    if request.method == 'POST':
        if not getattr(request.user, 'is_teacher', False):
            return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
        try:
            data = json.loads(request.body)
            lesson_plan_content = data.get('lesson_plan_content', '')
            question_type = data.get('question_type', 'MC')
            question_count = int(data.get('question_count', 5))

            if not lesson_plan_content.strip():
                return JsonResponse({'status': 'error', 'message': '教案内容不能为空。'}, status=400)

            client = OpenAI(
                api_key=settings.DASHSCOPE_API_KEY,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            question_type_map = {'MC': '选择题', 'TF': '判断题', 'SA': '简答题', 'PG': '编程题'}
            selected_question_type = question_type_map.get(question_type, '选择题')
            prompt = f"""
            作为一名经验丰富的教学专家，请根据以下教案内容，为我生成{question_count}道{selected_question_type}。
            每道题目需要包含问题、正确答案和详细的答案解析。答案解析应能清楚解释为什么该答案是正确的。
            请以以下 JSON 格式返回，确保 JSON 格式严格正确，不要包含任何额外文本或Markdown格式化：
            {{"questions": [
                {{"question_text": "题目内容", "question_type": "{question_type}", "correct_answer": "正确答案", "explanation": "详细解析"}},
                ...
            ]}}
            --- 教案内容 ---
            {lesson_plan_content}
            --- 题目生成开始 ---
            """
            completion = client.chat.completions.create(
                model="qwen-turbo",
                messages=[{'role': 'user', 'content': prompt}],
                temperature=0.8,
                response_format={"type": "json_object"},
            )
            generated_questions_str = completion.choices[0].message.content
            generated_questions_data = json.loads(generated_questions_str)
            
            if "questions" not in generated_questions_data or not isinstance(generated_questions_data["questions"], list):
                return JsonResponse({'status': 'error', 'message': 'AI返回的题目格式不正确。'}, status=500)

            return JsonResponse({'status': 'success', 'generated_questions': generated_questions_data['questions']})
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': 'AI返回的不是有效的JSON格式。'}, status=500)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'AI出题失败: {e}'}, status=500)

    return render(request, 'teacher_dashboard/generate_assessment.html', {'page_title': 'AI智能出题'})

@login_required
def analyze_learning_data_view(request):
    teacher = request.user

    participating_student_ids = StudentAnswer.objects.filter(
        assessment_attempt__assessment__teacher=teacher
    ).values_list('assessment_attempt__student_id', flat=True).distinct()
    
    participating_students = User.objects.filter(id__in=participating_student_ids)
    
    total_student_count = participating_students.count()
    total_assessment_count = Assessment.objects.filter(teacher=request.user, is_published=True).count()
    
    overall_responses = StudentAnswer.objects.filter(assessment_attempt__assessment__teacher=request.user)
    overall_correct_count = overall_responses.filter(is_correct=True).count()
    overall_total_count = overall_responses.count()
    overall_avg_accuracy = (overall_correct_count / overall_total_count * 100) if overall_total_count > 0 else 0

    recent_assessments = Assessment.objects.filter(teacher=request.user, is_published=True).order_by('-created_at')[:10]
    assessments_summary = []
    for ass in recent_assessments:
        submitted_count = AssessmentAttempt.objects.filter(assessment=ass).values('student_id').distinct().count()
        ass_correct = StudentAnswer.objects.filter(assessment_attempt__assessment=ass, is_correct=True).count()
        ass_total_qs = ass.questions.count()
        
        if submitted_count > 0 and ass_total_qs > 0:
            avg_score = (ass_correct / submitted_count) / ass_total_qs * 100
        else:
            avg_score = 0
            
        assessments_summary.append({
            'assessment': ass,
            'submitted_count': submitted_count,
            'total_students': total_student_count, 
            'average_score': round(avg_score, 2),
        })

    student_performance = participating_students.annotate(
        correct_answers=Count('student_answers', filter=Q(student_answers__is_correct=True, student_answers__assessment_attempt__assessment__teacher=teacher)),
        total_answers=Count('student_answers', filter=Q(student_answers__assessment_attempt__assessment__teacher=teacher))
    ).annotate(
        avg_accuracy=Cast(F('correct_answers'),FloatField()) / Cast(F('total_answers'), FloatField()) * 100
    ).order_by('-avg_accuracy')

    top_students = student_performance[:5]
    bottom_students = student_performance.order_by('avg_accuracy')[:5]

    context = {
        'page_title': '学情数据分析',
        'total_student_count': total_student_count,
        'total_assessment_count': total_assessment_count,
        'participating_student_count': total_student_count,
        'overall_avg_accuracy': round(overall_avg_accuracy, 2),
        'assessments_summary': assessments_summary,
        'top_students': top_students,
        'bottom_students': bottom_students,
    }
    return render(request, 'teacher_dashboard/analyze_learning_data.html', context)


@login_required
def question_bank(request):
    if not getattr(request.user, 'is_teacher', False): return redirect('users:login')
    search_query = request.GET.get('q', '')
    type_filter = request.GET.get('type', '')
    questions = request.user.questions.all().order_by('-created_at')
    if search_query: questions = questions.filter(question_text__icontains=search_query)
    if type_filter: questions = questions.filter(question_type=type_filter)
    paginator = Paginator(questions, 10)
    page_obj = paginator.get_page(request.GET.get('page'))
    context = {'page_obj': page_obj, 'search_query': search_query, 'type_filter': type_filter, 'page_title': '题库管理'}
    return render(request, 'teacher_dashboard/question_bank.html', context)

@csrf_exempt
@login_required
def question_detail_api(request, question_id):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
    try:
        question = Question.objects.get(id=question_id, teacher=request.user)
    except Question.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '题目不存在或您无权访问。'}, status=404)

    if request.method == 'GET':
        return JsonResponse({
            'status': 'success',
            'question': {
                'id': question.id,
                'question_text': question.question_text,
                'correct_answer': question.correct_answer,
                'question_type': question.get_question_type_display(),
            }
        })
    elif request.method == 'PUT':
        try:
            data = json.loads(request.body)
            question.question_text = data.get('question_text', question.question_text)
            question.correct_answer = data.get('correct_answer', question.correct_answer)
            question.save()
            return JsonResponse({'status': 'success', 'message': '题目更新成功！'})
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': '无效的请求格式。'}, status=400)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'更新失败: {e}'}, status=500)
    elif request.method == 'DELETE':
        try:
            question.delete()
            return JsonResponse({'status': 'success', 'message': '题目删除成功！'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'删除失败: {e}'}, status=500)
    return JsonResponse({'status': 'error', 'message': '不支持的请求方法。'}, status=405)


@login_required
@require_POST
def publish_assessment_api(request):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
    try:
        data = json.loads(request.body)
        title = data.get('title')
        question_ids = data.get('question_ids')
        if not title or not question_ids:
            return JsonResponse({'status': 'error', 'message': '缺少标题或题目ID。'}, status=400)
        assessment = Assessment.objects.create(
            title=title,
            teacher=request.user,
            is_published=True
        )
        questions = Question.objects.filter(id__in=question_ids, teacher=request.user)
        assessment.questions.add(*questions)
        return JsonResponse({'status': 'success', 'message': f'考核《{title}》已成功发布！'})
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': '无效的请求格式。'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'发布失败: {str(e)}'}, status=500)


@login_required
@require_POST
def question_api(request):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
    data = json.loads(request.body)
    action = data.get('action')
    if action == 'add':
        question_text = data.get('question_text')
        question_type = data.get('question_type')
        correct_answer = data.get('correct_answer')
        explanation = data.get('explanation')
        if not all([question_text, question_type, correct_answer, explanation]):
            return JsonResponse({'status': 'error', 'message': '缺少必要的题目信息'}, status=400)
        try:
            Question.objects.create(
                teacher=request.user,
                question_text=question_text,
                question_type=question_type,
                correct_answer=correct_answer,
                explanation=explanation
            )
            return JsonResponse({'status': 'success', 'message': '题目已成功添加到题库！'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'保存题目失败: {e}'}, status=500)
    return JsonResponse({'status': 'error', 'message': '无效的操作'}, status=400)


@login_required
def assessment_list_view(request):
    if not getattr(request.user, 'is_teacher', False): return redirect('users:login')
    assessments = request.user.assessments.all().order_by('-created_at')
    context = {'assessments': assessments, 'page_title': '我的考核'}
    return render(request, 'teacher_dashboard/assessment_list.html', context)


@login_required
def assessment_analysis_view(request, assessment_id):
    assessment = get_object_or_404(Assessment.objects.all(), id=assessment_id)
    
    submitted_student_ids = AssessmentAttempt.objects.filter(assessment=assessment).values_list('student_id', flat=True).distinct()
    submitted_students = User.objects.filter(id__in=submitted_student_ids)
    all_students = User.objects.filter(role='student')
    unsubmitted_students = all_students.exclude(id__in=submitted_student_ids)
    
    total_responses = StudentAnswer.objects.filter(assessment_attempt__assessment=assessment)
    correct_responses = total_responses.filter(is_correct=True).count()
    overall_accuracy = (correct_responses / total_responses.count() * 100) if total_responses.count() > 0 else 0
    
    # 获取每个题目的统计数据，用于图表展示
    question_stats = []
    for question in assessment.questions.all():
        responses_for_question = StudentAnswer.objects.filter(assessment_attempt__assessment=assessment, question=question)
        correct_count = responses_for_question.filter(is_correct=True).count()
        total_count = responses_for_question.count()
        accuracy = (correct_count / total_count * 100) if total_count > 0 else 0
        question_stats.append({
            'question': question,
            'correct_count': correct_count,
            'total_count': total_count,
            'accuracy': round(accuracy, 2),
        })
    
    # 获取学生详细信息和答题情况
    student_details = []
    for student in all_students:
        attempts = AssessmentAttempt.objects.filter(assessment=assessment, student=student)
        has_submitted = attempts.exists()
        
        student_detail = {
            'student': student,
            'has_submitted': has_submitted,
            'submission_time': attempts.first().created_at if has_submitted else None,
            'score': 0,
            'responses_details': []
        }
        
        if has_submitted:
            attempt = attempts.first()
            answers = StudentAnswer.objects.filter(assessment_attempt=attempt)
            correct_count = answers.filter(is_correct=True).count()
            total_questions = assessment.questions.count()
            
            # 计算分数
            if total_questions > 0:
                student_detail['score'] = (correct_count / total_questions) * 100
            
            # 获取学生每个题目的回答情况
            for question in assessment.questions.all():
                try:
                    answer = answers.get(question=question)
                    student_detail['responses_details'].append({
                        'question': question,
                        'student_answer': answer.answer_text,
                        'is_correct': answer.is_correct
                    })
                except StudentAnswer.DoesNotExist:
                    # 学生没有回答这个问题
                    student_detail['responses_details'].append({
                        'question': question,
                        'student_answer': '未作答',
                        'is_correct': False
                    })
        
        student_details.append(student_detail)
    
    # 获取错题排名
    wrong_answer_stats = []
    for question in assessment.questions.all():
        answers = StudentAnswer.objects.filter(assessment_attempt__assessment=assessment, question=question)
        wrong_count = answers.filter(is_correct=False).count()
        total_count = answers.count()
        accuracy = ((total_count - wrong_count) / total_count * 100) if total_count > 0 else 100
        wrong_answer_stats.append({
            'question': question,
            'wrong_count': wrong_count,
            'total_count': total_count,
            'accuracy': accuracy
        })
    
    # 按错误率排序，获取前5个错题
    top_wrong_questions = sorted(wrong_answer_stats, key=lambda x: x['accuracy'])[:5]

    context = {
        'assessment': assessment,
        'page_title': f"考核分析: {assessment.title}",
        'submitted_students': submitted_students,
        'unsubmitted_students': unsubmitted_students,
        'overall_accuracy': round(overall_accuracy, 2),
        'question_stats': question_stats,
        'student_details': student_details,
        'top_wrong_questions': top_wrong_questions
    }
    return render(request, 'teacher_dashboard/assessment_analysis.html', context)


@login_required
@require_POST
def publish_assessment(request, assessment_id):
    if not getattr(request.user, 'is_teacher', False): return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
    try:
        assessment = request.user.assessments.get(id=assessment_id)
        assessment.is_published = True
        assessment.save()
        return JsonResponse({'status': 'success', 'message': '考核发布成功！'})
    except Assessment.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '考核不存在'}, status=404)


@login_required
@require_POST
def save_teaching_plan(request):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': 'Authentication failed.'}, status=403)
    title = request.POST.get('title')
    content = request.POST.get('content')
    if not title or not content:
        return JsonResponse({'status': 'error', 'message': 'Title and content cannot be empty.'}, status=400)
    try:
        plan = TeachingPlan.objects.create(
            teacher=request.user,
            title=title,
            content=content
        )
        return JsonResponse({
            'status': 'success',
            'message': '教案已成功保存!',
            'plan': {'id': plan.id, 'title': plan.title}
        })
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'Failed to save plan: {str(e)}'}, status=500)


@login_required
@require_POST
def export_teaching_plan(request):
    if not getattr(request.user, 'is_teacher', False):
        return HttpResponse("权限不足", status=403)
    title = request.POST.get('title', '未命名教案')
    content = request.POST.get('content', '')
    if not content:
        return HttpResponse("教案内容不能为空", status=400)
    try:
        document = docx.Document()
        document.add_heading(title, level=1)
        for paragraph in content.split('\n'):
            if paragraph.strip():
                document.add_paragraph(paragraph)
        buffer = BytesIO()
        document.save(buffer)
        buffer.seek(0)
        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        safe_title = title.replace('/', '_').replace('\\', '_')
        response['Content-Disposition'] = f'attachment; filename="{safe_title}.docx"'
        return response
    except Exception as e:
        return HttpResponse(f"导出失败: {str(e)}", status=500)


@login_required
@require_POST
def generate_questions(request, plan_id):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': 'Authentication failed.'}, status=403)
    try:
        plan = TeachingPlan.objects.get(id=plan_id, teacher=request.user)
        data = json.loads(request.body)
        question_requests = data.get('requests', {})
        if not question_requests:
            return JsonResponse({'status': 'error', 'message': 'No question types or quantities specified.'}, status=400)
        prompt_parts = []
        type_map = {'MC': '选择题', 'TF': '判断题', 'SA': '简答题'}
        for q_type, count in question_requests.items():
            if int(count) > 0:
                prompt_parts.append(f"{count}道{type_map.get(q_type, '')}")
        
        if not prompt_parts:
             return JsonResponse({'status': 'error', 'message': 'Invalid question counts.'}, status=400)
        prompt = f"""
        作为一名专业的教学设计师和学科专家，请根据以下教案的核心内容，为我生成高质量的考核题目：{', '.join(prompt_parts)}。
        ## 出题核心要求：
        1.  **深度与质量**: 题目应考察学生对核心概念的深层理解、应用或分析能力，避免简单的、可直接从原文找到答案的表面问题。
        2.  **相关性**: 每道题都必须与教案的教学目标和重点内容紧密相关。
        3.  **清晰度**: 问题、选项和答案必须语言清晰、准确、无歧义。
        4.  **严格格式**: 请严格按照以下JSON格式返回，不要包含任何额外说明或Markdown标记。
        ## 格式定义：
        - 对于 **选择题 (MC)**:
            - `question` 字段为问题题干。
            - `options` 字段必须是一个包含4个字符串选项的数组。
            - `answer` 字段必须是正确选项对应的字母 (例如: "A", "B", "C", 或 "D")。
        - 对于 **判断题 (TF)**, `answer` 字段应为 '正确' 或 '错误'。
        - 对于 **简答题 (SA)** 或 **编程题 (PG)**, `answer` 字段应为精准的参考答案或代码，`options`字段不存在。
        ```json
        {{
          "questions": [
            {{
              "type": "MC",
              "question": "在MVC架构中，哪个部分负责处理用户输入和交互？",
              "options": ["模型 (Model)", "视图 (View)", "控制器 (Controller)", "数据库 (Database)"],
              "answer": "C",
              "explanation": "控制器是处理用户输入、与模型和视图交互的核心组件。"
            }},
            {{
              "type": "TF",
              "question": "HTML是一种编程语言。",
              "answer": "错误",
              "explanation": "HTML是一种标记语言，用于定义网页的结构，而不是编程语言。"
            }}
          ]
        }}
        ```
        --- 教案内容 ---
        {plan.content}
        --- 题目生成开始 ---
        """
        client = get_dashscope_client()
        if not client:
            return JsonResponse({'status': 'error', 'message': 'AI service is not configured.'}, status=500)
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[{'role': 'user', 'content': prompt}],
            temperature=0.5,
            response_format={"type": "json_object"},
        )
        message_content = completion.choices[0].message.content
        newly_generated_questions = []
        if message_content:
            response_data = json.loads(message_content)
            if "questions" in response_data and isinstance(response_data["questions"], list):
                for q_data in response_data["questions"]:
                    question_to_save = q_data.get("question", "")
                    if q_data.get("type") == "MC" and isinstance(q_data.get("options"), list):
                        options_text = "\\n".join([f"{chr(65+i)}. {opt}" for i, opt in enumerate(q_data.get("options"))])
                        question_to_save += f"\\n\\n{options_text}"
                    Question.objects.create(
                        teacher=request.user, teaching_plan=plan,
                        question_text=question_to_save,
                        question_type=q_data.get("type", "SA"),
                        correct_answer=q_data.get("answer", ""),
                        explanation=q_data.get("explanation", ""),
                    )
                newly_generated_questions = response_data["questions"]
        return JsonResponse({'status': 'success', 'questions': newly_generated_questions})
    except TeachingPlan.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '指定的教案不存在。'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'题目生成失败: {str(e)}'}, status=500)
        
@login_required
@require_POST
def add_question_to_bank(request):
    try:
        data = json.loads(request.body)
        plan_id = data.get('plan_id')
        question_type_str = data.get('question_type')
        question_text = data.get('question_text')
        answer_text = data.get('answer_text')
        if not all([plan_id, question_type_str, question_text, answer_text]):
            return JsonResponse({'status': 'error', 'message': '请求中缺少必要的数据。'}, status=400)
        plan = get_object_or_404(TeachingPlan, id=plan_id, teacher=request.user)
        type_mapping = {v: k for k, v in Question.QUESTION_TYPE_CHOICES}
        type_mapping.update({k: k for k, v in Question.QUESTION_TYPE_CHOICES}) 
        question_type_code = type_mapping.get(question_type_str)
        if not question_type_code:
            return JsonResponse({'status': 'error', 'message': f'无效的题目类型: {question_type_str}'}, status=400)
        if Question.objects.filter(teaching_plan=plan, question_text__exact=question_text).exists():
             return JsonResponse({'status': 'success', 'message': '该题目已存在于题库中。'})
        Question.objects.create(
            teacher=request.user,
            teaching_plan=plan,
            question_type=question_type_code,
            question_text=question_text,
            correct_answer=answer_text
        )
        return JsonResponse({'status': 'success', 'message': '题目已成功添加到题库！'})
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': '无效的请求格式 (Invalid JSON).'}, status=400)
    except TeachingPlan.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '指定的教案不存在或您无权访问。'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'服务器内部错误: {str(e)}'}, status=500)


@login_required
def assessment_list(request):
    if not getattr(request.user, 'is_teacher', False):
        return redirect('users:login')
    assessments = Assessment.objects.filter(teacher=request.user).order_by('-created_at')
    return render(request, 'teacher_dashboard/assessment_list.html', {'assessments': assessments})

@login_required
def generate_assessment(request):
    if not getattr(request.user, 'is_teacher', False):
        return redirect('users:login')
    return render(request, 'teacher_dashboard/generate_assessment.html')


@login_required
def courseware_management(request):
    if not getattr(request.user, 'is_teacher', False): return redirect('users:login')
    if request.method == 'POST':
        Courseware.objects.create(
            teacher=request.user,
            title=request.POST.get('title'),
            description=request.POST.get('description'),
            subject=request.POST.get('subject'),
            file=request.FILES.get('file')
        )
        return redirect('teacher_dashboard:courseware_management')
    coursewares = request.user.courseware_set.all().order_by('-upload_date')
    context = {'coursewares': coursewares, 'page_title': '课件资源管理'}
    return render(request, 'teacher_dashboard/courseware_management.html', context)

@login_required
@require_POST
def save_lesson_plan_as_docx(request):
    if not getattr(request.user, 'is_teacher', False):
        return JsonResponse({'status': 'error', 'message': '认证失败'}, status=403)
    try:
        data = json.loads(request.body)
        lesson_plan_content = data.get('lesson_plan_content', '')
        lesson_plan_title = data.get('lesson_plan_title', 'AI生成教案')
        if not lesson_plan_content:
            return JsonResponse({'status': 'error', 'message': '教案内容不能为空。'}, status=400)
        document = docx.Document()
        document.add_heading(lesson_plan_title, level=1)
        for paragraph in lesson_plan_content.split('\n'):
            document.add_paragraph(paragraph)
        f = BytesIO()
        document.save(f)
        f.seek(0)
        response = HttpResponse(
            f.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        response['Content-Disposition'] = f'attachment; filename="{lesson_plan_title}.docx"'
        return response
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': f'保存教案失败: {e}'}, status=500)