from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from teacher_dashboard.models import Question, Assessment
from student_dashboard.models import AssessmentAttempt, StudentAnswer
from users.models import User
from datetime import datetime, timedelta
import random

class Command(BaseCommand):
    help = 'Seed sample data for teacher dashboard charts'

    def handle(self, *args, **options):
        self.stdout.write("Creating sample data for teacher dashboard...")
        
        # 1. Create a teacher user if not exists
        teacher, created = User.objects.get_or_create(
            username='teacher1',
            defaults={
                'email': '<EMAIL>',
                'password': make_password('password123'),
                'role': 'teacher',
                'first_name': '张',
                'last_name': '老师'
            }
        )
        if created:
            self.stdout.write(f"Created teacher: {teacher.username}")
        else:
            self.stdout.write(f"Teacher already exists: {teacher.username}")
        
        # 2. Create student users if not exist
        students = []
        for i in range(1, 6):
            student, created = User.objects.get_or_create(
                username=f'student{i}',
                defaults={
                    'email': f'student{i}@example.com',
                    'password': make_password('password123'),
                    'role': 'student',
                    'first_name': f'学生{i}',
                    'last_name': '同学'
                }
            )
            students.append(student)
            if created:
                self.stdout.write(f"Created student: {student.username}")
        
        # 3. Create sample questions
        sample_questions = [
            {
                'question_text': 'Python中如何定义一个函数？',
                'question_type': 'MC',
                'correct_answer': 'def function_name():',
                'explanation': '使用def关键字定义函数'
            },
            {
                'question_text': '什么是面向对象编程？',
                'question_type': 'SA',
                'correct_answer': '面向对象编程是一种编程范式，以对象为中心组织代码',
                'explanation': 'OOP的核心概念包括封装、继承和多态'
            },
            {
                'question_text': 'Python是解释型语言吗？',
                'question_type': 'TF',
                'correct_answer': 'True',
                'explanation': 'Python是解释型语言，代码在运行时被解释执行'
            },
            {
                'question_text': '编写一个计算斐波那契数列的函数',
                'question_type': 'PG',
                'correct_answer': 'def fibonacci(n): ...',
                'explanation': '递归或迭代方式实现斐波那契数列'
            },
            {
                'question_text': 'HTML的全称是什么？',
                'question_type': 'MC',
                'correct_answer': 'HyperText Markup Language',
                'explanation': 'HTML是超文本标记语言'
            },
            {
                'question_text': 'CSS用于控制网页样式吗？',
                'question_type': 'TF',
                'correct_answer': 'True',
                'explanation': 'CSS层叠样式表用于控制网页的外观和布局'
            }
        ]
        
        questions = []
        for q_data in sample_questions:
            question, created = Question.objects.get_or_create(
                question_text=q_data['question_text'],
                defaults={
                    'question_type': q_data['question_type'],
                    'correct_answer': q_data['correct_answer'],
                    'explanation': q_data['explanation'],
                    'teacher': teacher
                }
            )
            questions.append(question)
            if created:
                self.stdout.write(f"Created question: {question.question_text[:50]}...")
        
        # 4. Create sample assessments
        assessment_data = [
            {
                'title': 'Python基础测试',
                'questions': questions[:3]
            },
            {
                'title': 'Web开发考核',
                'questions': questions[3:]
            },
            {
                'title': '综合练习',
                'questions': questions[:4]
            }
        ]
        
        assessments = []
        for a_data in assessment_data:
            assessment, created = Assessment.objects.get_or_create(
                title=a_data['title'],
                teacher=teacher,
                defaults={
                    'is_published': True
                }
            )
            if created:
                assessment.questions.set(a_data['questions'])
                self.stdout.write(f"Created assessment: {assessment.title}")
            assessments.append(assessment)
        
        # 5. Create sample assessment attempts and answers
        for assessment in assessments:
            # Each assessment will have attempts from 3-5 students
            num_students = random.randint(3, 5)
            selected_students = random.sample(students, num_students)
            
            for student in selected_students:
                # Create assessment attempt
                attempt, created = AssessmentAttempt.objects.get_or_create(
                    student=student,
                    assessment=assessment,
                    defaults={
                        'start_time': datetime.now() - timedelta(days=random.randint(1, 30)),
                        'end_time': datetime.now() - timedelta(days=random.randint(1, 30)),
                        'score': random.uniform(60, 95)
                    }
                )
                
                if created:
                    # Create answers for each question in the assessment
                    for question in assessment.questions.all():
                        # Random correctness (70-90% chance of being correct)
                        is_correct = random.random() < random.uniform(0.7, 0.9)
                        
                        StudentAnswer.objects.get_or_create(
                            assessment_attempt=attempt,
                            question=question,
                            defaults={
                                'answer_text': f'Student answer for {question.question_text[:20]}...',
                                'is_correct': is_correct
                            }
                        )
                    
                    self.stdout.write(f"Created attempt for {student.username} on {assessment.title}")
        
        self.stdout.write(self.style.SUCCESS("\nSample data creation completed!"))
        self.stdout.write(f"Total questions: {Question.objects.count()}")
        self.stdout.write(f"Total assessments: {Assessment.objects.count()}")
        self.stdout.write(f"Total attempts: {AssessmentAttempt.objects.count()}")
        self.stdout.write(f"Total answers: {StudentAnswer.objects.count()}")
