# Generated manually to add explanation field to StudentResponse

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teacher_dashboard', '0003_teachingplan_question_teaching_plan'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentresponse',
            name='is_mastered',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='studentresponse',
            name='explanation',
            field=models.TextField(blank=True, null=True),
        ),
    ]
