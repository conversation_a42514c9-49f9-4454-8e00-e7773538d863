# Generated by Django 4.2.7 on 2025-07-14 05:39

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Assessment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_published", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="AssessmentQuestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("order", models.PositiveIntegerField()),
            ],
            options={
                "ordering": ["order"],
            },
        ),
        migrations.CreateModel(
            name="Courseware",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(default="Untitled", max_length=200)),
                ("description", models.TextField(blank=True, default="")),
                ("file", models.FileField(upload_to="courseware/")),
                (
                    "upload_date",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("subject", models.CharField(blank=True, default="", max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Question",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question_text", models.TextField()),
                (
                    "question_type",
                    models.CharField(
                        choices=[
                            ("MC", "Multiple Choice"),
                            ("TF", "True/False"),
                            ("SA", "Short Answer"),
                            ("PG", "Programming Question"),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    "correct_answer",
                    models.TextField(help_text="The correct answer for the question."),
                ),
                ("explanation", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="StudentResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("answer_text", models.TextField()),
                ("is_correct", models.BooleanField()),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assessment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responses",
                        to="teacher_dashboard.assessment",
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="teacher_dashboard.question",
                    ),
                ),
            ],
        ),
    ]
