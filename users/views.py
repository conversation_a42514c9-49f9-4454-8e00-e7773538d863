from django.shortcuts import render, redirect
from django.contrib.auth import login, logout, authenticate
from .forms import UserLoginForm, UserCreationForm
from django.contrib.auth.decorators import login_required

# Create your views here.

@login_required
def home(request):
    if request.user.is_superuser or getattr(request.user, 'is_admin', False):
        return redirect('admin_dashboard:dashboard')
    elif getattr(request.user, 'is_teacher', False):
        return redirect('teacher_dashboard:dashboard')
    elif getattr(request.user, 'is_student', False):
        return redirect('student_dashboard:dashboard')
    else:
        # Fallback for users with no specific role, or handle as an error
        return redirect('users:login')

def user_login(request):
    if request.method == 'POST':
        form = UserLoginForm(request=request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            return redirect('users:home')
    else:
        form = UserLoginForm()
    return render(request, 'users/login.html', {'form': form})

def user_logout(request):
    logout(request)
    return redirect('users:login')

def user_registration(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            return redirect('users:home')
    else:
        form = UserCreationForm()
    return render(request, 'users/register.html', {'form': form})
