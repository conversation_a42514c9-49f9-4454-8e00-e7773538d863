<!DOCTYPE html>
<html>
<head>
    <title>首页</title>
    <!-- 引入 Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .home-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h2 {
            color: #0056b3;
            margin-bottom: 20px;
        }
        .btn-group-lg .btn {
            padding: 15px 30px;
            font-size: 1.25rem;
            border-radius: .5rem;
        }
        .mt-4 {
            margin-top: 1.5rem !important;
        }
    </style>
</head>
<body>
    <div class="home-container">
        <h2>欢迎，{{ user.username }}！</h2>
        <p class="lead">您的角色是：<strong>{{ user.role }}</strong></p>

        <div class="mt-4">
            {% if user.role == "teacher" %}
                <a href="{% url 'teacher_dashboard' %}" class="btn btn-primary btn-lg">前往教师仪表板</a>
            {% elif user.role == "student" %}
                <a href="{% url 'student_dashboard' %}" class="btn btn-success btn-lg">前往学生仪表板</a>
            {% elif user.role == "admin" %}
                <a href="{% url 'admin_dashboard' %}" class="btn btn-info btn-lg">前往管理员仪表板</a>
            {% else %}
                <p class="alert alert-warning">您的角色未定义或不受支持，请联系管理员。</p>
            {% endif %}
        </div>

        <div class="mt-4">
            <a href="{% url 'logout' %}" class="btn btn-outline-secondary btn-sm">注销</a>
        </div>
    </div>
    <!-- 引入 Bootstrap 5 JS (可选，如果不需要JS功能可以不引入) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 