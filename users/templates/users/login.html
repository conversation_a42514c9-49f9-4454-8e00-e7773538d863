<!DOCTYPE html>
<html>
<head>
    <title>登录</title>
    <!-- 引入 Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .main-title {
            color: #0056b3;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
        }
        .login-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px; /* Increased max-width for better alignment */
        }
        h2 {
            color: #0056b3;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-control {
            /* margin-bottom: 15px; */ /* Removed as mb-3 on div handles spacing */
        }
        .btn-primary {
            width: 100%;
            padding: 10px;
            font-size: 1.1rem;
            margin-top: 20px; /* Added margin for button */
        }
        .form-group.row {
            align-items: center; /* Vertically align label and input */
            margin-bottom: 15px; /* Spacing between form rows */
        }
    </style>
</head>
<body>
    <h1 class="main-title">教学实训软件</h1>
    <div class="login-container">
        <h2>用户登录</h2>
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger" role="alert">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            {% for field in form %}
                <div class="form-group row mb-3">
                    <label for="{{ field.id_for_label }}" class="col-md-4 col-form-label">{{ field.label }}</label>
                    <div class="col-md-8">
                        {{ field }} 
                        {% if field.help_text %}
                            <div class="form-text">{{ field.help_text }}</div>
                        {% endif %}
                        {% for error in field.errors %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
            <button type="submit" class="btn btn-primary">登录</button>
        </form>
        {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    <!-- 引入 Bootstrap 5 JS (可选，如果不需要JS功能可以不引入) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 