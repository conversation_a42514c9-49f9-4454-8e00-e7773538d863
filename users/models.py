from django.db import models
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    ROLE_CHOICES = (
        ("teacher", "教师"),
        ("student", "学生"),
        ("admin", "管理员"),
    )
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default="student")

    @property
    def is_admin(self):
        return self.role == 'admin'

    @property
    def is_teacher(self):
        return self.role == 'teacher'

    @property
    def is_student(self):
        return self.role == 'student'

    def __str__(self):
        return self.username
