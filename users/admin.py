from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User

# Register your models here.

class CustomUserAdmin(UserAdmin):
    fieldsets = UserAdmin.fieldsets + ( # For editing existing users
        (None, {'fields': ('role',)}),
    )
    add_fieldsets = UserAdmin.add_fieldsets + ( # For creating new users
        (None, {'fields': ('role',)}),
    )

admin.site.register(User, CustomUserAdmin)
