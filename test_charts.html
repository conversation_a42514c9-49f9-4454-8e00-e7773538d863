<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Chart Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .stat-card {
            border-left-width: 5px;
        }
        .border-primary { border-left-color: #0d6efd !important; }
        .border-success { border-left-color: #198754 !important; }
        .border-info { border-left-color: #0dcaf0 !important; }
        .border-warning { border-left-color: #ffc107 !important; }
    </style>
</head>
<body>
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">教师后台 - 图表测试</h1>

    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-primary stat-card shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">题库题目总数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">109</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-success stat-card shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">已发布考核数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">2</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-info stat-card shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">覆盖学生数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">5</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-warning stat-card shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">总体学习掌握度</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">75%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">近期考核正确率趋势</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="accuracyTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">题库题型分布</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4" style="height: 300px;">
                        <canvas id="questionTypeDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 高频错题列表 -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">高频错题 Top 5</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Python中如何定义一个函数？
                            <span class="badge bg-danger rounded-pill">15 次错误</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            什么是面向对象编程？
                            <span class="badge bg-danger rounded-pill">12 次错误</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            如何处理Python中的异常？
                            <span class="badge bg-danger rounded-pill">10 次错误</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Helper function to destroy existing chart
function destroyChart(chartId) {
    let chart = Chart.getChart(chartId);
    if (chart) {
        chart.destroy();
    }
}

// 1. Accuracy Trend Chart with test data
destroyChart('accuracyTrendChart');
var ctx1 = document.getElementById("accuracyTrendChart").getContext('2d');

var accuracyTrendChart = new Chart(ctx1, {
    type: 'line',
    data: {
        labels: ['Python基础测试', 'Web开发考核', '数据结构练习', '算法设计题', '项目实战'],
        datasets: [{
            label: "正确率 (%)",
            data: [85, 78, 92, 67, 88],
            backgroundColor: 'rgba(78, 115, 223, 0.05)',
            borderColor: 'rgba(78, 115, 223, 1)',
            borderWidth: 2,
            pointBackgroundColor: 'rgba(78, 115, 223, 1)',
            pointRadius: 3,
            pointHitRadius: 10,
        }]
    },
    options: {
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + context.parsed.y + '%';
                    }
                }
            }
        }
    }
});

// 2. Question Type Distribution Chart with test data
destroyChart('questionTypeDistributionChart');
var ctx2 = document.getElementById("questionTypeDistributionChart").getContext('2d');

var questionTypeDistributionChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['选择题', '判断题', '简答题', '编程题'],
        datasets: [{
            data: [45, 25, 20, 19],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#dda20a'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }]
    },
    options: {
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        let label = context.label || '';
                        let value = context.raw;
                        let total = context.chart.getDatasetMeta(0).total;
                        let percentage = total > 0 ? (value / total * 100).toFixed(2) + '%' : '0%';
                        return label + ': ' + value + ' (' + percentage + ')';
                    }
                }
            }
        }
    }
});

console.log('Charts initialized successfully!');
</script>

</body>
</html>
