<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>课件上传测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <h2>课件上传功能测试</h2>
    
    <!-- Upload Form Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">上传新课件</h6>
        </div>
        <div class="card-body">
            <form id="courseware-upload-form" method="post" enctype="multipart/form-data" action="http://127.0.0.1:8000/teacher-dashboard/courseware/">
                <input type="hidden" name="csrfmiddlewaretoken" value="test-token">
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="title">标题</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="例如：高一数学上册第一单元" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="subject">学科</label>
                        <input type="text" class="form-control" id="subject" name="subject" placeholder="例如：数学" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="description">描述</label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="简要描述课件内容..."></textarea>
                </div>
                <div class="form-group mt-3">
                    <label for="file">选择文件</label>
                    <input type="file" class="form-control-file" id="file" name="file" required>
                </div>
                <button type="submit" class="btn btn-primary mt-3" id="upload-btn">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span>
                    <i class="fas fa-upload fa-sm text-white-50"></i> 
                    <span class="btn-text">上传</span>
                </button>
            </form>
            
            <!-- Success/Error Messages -->
            <div id="upload-message" class="mt-3" style="display: none;"></div>
        </div>
    </div>

    <!-- Test Results -->
    <div class="card">
        <div class="card-header">
            <h6>测试结果</h6>
        </div>
        <div class="card-body">
            <div id="test-results">
                <p class="text-muted">填写表单并点击上传来测试功能...</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('courseware-upload-form');
    const uploadBtn = document.getElementById('upload-btn');
    const messageDiv = document.getElementById('upload-message');
    const testResults = document.getElementById('test-results');
    const spinner = uploadBtn.querySelector('.spinner-border');
    const btnText = uploadBtn.querySelector('.btn-text');
    
    if (uploadForm) {
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            testResults.innerHTML = '<p class="text-info">开始测试上传功能...</p>';
            
            // Show loading state
            uploadBtn.disabled = true;
            spinner.style.display = 'inline-block';
            btnText.textContent = '上传中...';
            messageDiv.style.display = 'none';
            
            try {
                const formData = new FormData(uploadForm);
                
                testResults.innerHTML += '<p class="text-info">发送AJAX请求...</p>';
                
                const response = await fetch(uploadForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                testResults.innerHTML += `<p class="text-info">收到响应，状态码: ${response.status}</p>`;
                
                const result = await response.json();
                
                testResults.innerHTML += `<p class="text-info">解析JSON响应: ${JSON.stringify(result, null, 2)}</p>`;
                
                if (result.status === 'success') {
                    // Show success message
                    messageDiv.className = 'alert alert-success mt-3';
                    messageDiv.textContent = result.message;
                    messageDiv.style.display = 'block';
                    
                    testResults.innerHTML += '<p class="text-success"><strong>✅ 测试成功！上传功能正常工作。</strong></p>';
                    
                    // Reset form
                    uploadForm.reset();
                    
                } else {
                    // Show error message
                    messageDiv.className = 'alert alert-danger mt-3';
                    messageDiv.textContent = result.message || '上传失败，请重试';
                    messageDiv.style.display = 'block';
                    
                    testResults.innerHTML += '<p class="text-warning"><strong>⚠️ 服务器返回错误，但AJAX通信正常。</strong></p>';
                }
                
            } catch (error) {
                console.error('Upload error:', error);
                messageDiv.className = 'alert alert-danger mt-3';
                messageDiv.textContent = '网络错误，请检查连接后重试';
                messageDiv.style.display = 'block';
                
                testResults.innerHTML += `<p class="text-danger"><strong>❌ 测试失败：${error.message}</strong></p>`;
            } finally {
                // Reset button state
                uploadBtn.disabled = false;
                spinner.style.display = 'none';
                btnText.textContent = '上传';
            }
        });
    }
});
</script>

</body>
</html>
