<!DOCTYPE html>
<html>
<head>
    <title>实时练习与评估</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
        }
        .card-header {
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">AI 个性化练习</h4>
        </div>
        <div class="card-body">
            <p class="card-text">在这里，AI可以根据您的历史错题和学习情况，为您量身定制练习题，助您攻克薄弱环节。</p>

            {% if error %}
                <div class="alert alert-danger">{{ error }}</div>
            {% endif %}

            <!-- 第一步：选择练习要求 -->
            <div class="card mb-4">
                <div class="card-header">
                    第一步：请选择练习要求
                </div>
                <div class="card-body p-3">
                    <form method="post" action="{% url 'realtime_practice_evaluation' %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="generate_question">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="subject" class="form-label">科目</label>
                                <select class="form-select" id="subject" name="subject" required>
                                    <option value="">请选择科目</option>
                                    {% for subject in subjects %}
                                        <option value="{{ subject }}" {% if subject == selected_subject %}selected{% endif %}>{{ subject }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="difficulty" class="form-label">难度</label>
                                <select class="form-select" id="difficulty" name="difficulty">
                                    <option value="入门" {% if selected_difficulty == '入门' %}selected{% endif %}>入门</option>
                                    <option value="基础" {% if selected_difficulty == '基础' %}selected{% endif %}>基础</option>
                                    <option value="进阶" {% if selected_difficulty == '进阶' %}selected{% endif %}>进阶</option>
                                    <option value="挑战" {% if selected_difficulty == '挑战' %}selected{% endif %}>挑战</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="num_questions" class="form-label">题目数量</label>
                                <input type="number" class="form-control" id="num_questions" name="num_questions" value="{{ num_questions|default:1 }}" min="1" max="10">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-cogs me-2"></i>生成练习题
                        </button>
                    </form>
                </div>
            </div>

            <!-- 第二步：请作答 -->
            {% if practice_questions and not evaluation_results %}
            <div class="card">
                <div class="card-header">
                    第二步：请作答
                </div>
                <div class="card-body p-3">
                    <form method="post" action="{% url 'realtime_practice_evaluation' %}" id="answer-form">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="submit_answer">
                        
                        {% for question in practice_questions %}
                        <div class="mb-4">
                            <p class="fw-bold">题目 {{ forloop.counter }}:</p>
                            
                            {% if question.question_type == '选择题' %}
                                <p>{{ question.question_text }}</p>
                                <div class="ms-3">
                                    {% for option in question.options %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.parentloop.counter }}" id="option_{{ forloop.parentloop.counter }}_{{ forloop.counter }}" value="{{ option }}" required>
                                        <label class="form-check-label" for="option_{{ forloop.parentloop.counter }}_{{ forloop.counter }}">
                                            {{ option }}
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% elif question.question_type == '判断题' %}
                                <p>{{ question.question_text }}</p>
                                <div class="ms-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.counter }}" id="option_{{ forloop.counter }}_true" value="正确" required>
                                        <label class="form-check-label" for="option_{{ forloop.counter }}_true">
                                            正确
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="student_answer_{{ forloop.counter }}" id="option_{{ forloop.counter }}_false" value="错误" required>
                                        <label class="form-check-label" for="option_{{ forloop.counter }}_false">
                                            错误
                                        </label>
                                    </div>
                                </div>
                            {% else %}
                                <p>{{ question.question_text }}</p>
                                <div class="form-floating">
                                    <textarea class="form-control" name="student_answer_{{ forloop.counter }}" id="student_answer_{{ forloop.counter }}" style="height: 100px" required></textarea>
                                    <label for="student_answer_{{ forloop.counter }}">您的答案</label>
                                </div>
                            {% endif %}
                        </div>
                        {% endfor %}

                        <button id="submit-btn" type="submit" class="btn btn-success">
                            <i class="fas fa-check-circle me-2"></i>提交答案
                        </button>
                        <div id="loading-message" class="alert alert-info mt-3" style="display: none;">
                            <i class="fas fa-spinner fa-spin me-2"></i> 答案已提交，AI 正在批改中，请稍候...
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <!-- 第三步：查看结果 -->
            {% if evaluation_results %}
            <div class="card mt-4">
                <div class="card-header">
                    第三步：练习结果与建议
                </div>
                <div class="card-body">
                    {% for result in evaluation_results %}
                    <div class="card mb-3 {% if result.is_correct %}border-success{% else %}border-danger{% endif %}">
                        <div class="card-header {% if result.is_correct %}bg-success-subtle{% else %}bg-danger-subtle{% endif %}">
                            <p class="fw-bold mb-0">题目 {{ forloop.counter }} - {% if result.is_correct %}回答正确{% else %}回答错误{% endif %}</p>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <p class="mb-1"><strong>题目：</strong></p>
                                {% with practice_questions|slice:":forloop.counter"|last as question %}
                                    {% if question.question_type == '选择题' %}
                                        <p class="mb-1">{{ question.question_text }}</p>
                                        {% if question.options %}
                                        <ul class="list-unstyled ps-3">
                                        {% for option in question.options %}
                                            <li>{{ option }}</li>
                                        {% endfor %}
                                        </ul>
                                        {% endif %}
                                    {% else %}
                                        <p>{{ question.question_text }}</p>
                                    {% endif %}
                                    <p class="mb-1"><strong>正确答案：</strong> <span class="text-success">{{ question.correct_answer }}</span></p>
                                {% endwith %}
                            </div>
                            <p class="mb-1"><strong>你的答案：</strong> <span class="{% if result.is_correct %}text-success{% else %}text-danger{% endif %}">{{ result.student_answer }}</span></p>
                            <hr>
                            <p class="mb-1"><strong>AI智能解析：</strong></p>
                            <div class="p-3 bg-light rounded">
                                {{ result.suggestion|linebreaksbr }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const answerForm = document.getElementById('answer-form');
        if (answerForm) {
            answerForm.addEventListener('submit', function() {
                const submitBtn = document.getElementById('submit-btn');
                const loadingMsg = document.getElementById('loading-message');
                if (submitBtn && loadingMsg) {
                    submitBtn.style.display = 'none';
                    loadingMsg.style.display = 'block';
                }
            });
        }
    });
</script>
</body>
</html> 