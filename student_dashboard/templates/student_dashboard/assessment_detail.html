<!DOCTYPE html>
<html>
<head>
    <title>{{ assessment.title }} - 考核详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
        }
        .question-card {
            margin-bottom: 1.5rem;
        }
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1050; /* Higher than other elements */
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <div id="loading-overlay" style="display: none;">
        <div class="spinner-border mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5>正在提交，请稍候...</h5>
    </div>

    <div class="container my-5">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h2 class="mb-0">{{ assessment.title }}</h2>
                <p class="mb-0">由 {{ assessment.teacher.username }} 老师发布</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{% url 'student_dashboard:assessment_detail' assessment.id %}" id="assessment-form">
                    {% csrf_token %}
                    {% for question in assessment.questions.all %}
                        <div class="card question-card">
                            <div class="card-header">
                                <strong>问题 {{ forloop.counter }}: 【{{ question.question_type }}】</strong>
                            </div>
                            <div class="card-body">
                                <p class="card-text">{{ question.question_text|linebreaksbr }}</p>
                                
                                {% if question.options %}
                                    <div class="mb-3">
                                        {% for option in question.options %}
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="question_{{ question.id }}" id="option_{{ question.id }}_{{ forloop.counter }}" value="{{ option }}">
                                                <label class="form-check-label" for="option_{{ question.id }}_{{ forloop.counter }}">
                                                    {{ option }}
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                     <div class="mb-3">
                                        <label for="answer_{{ question.id }}" class="form-label">您的答案:</label>
                                        <textarea class="form-control" id="answer_{{ question.id }}" name="question_{{ question.id }}" rows="3"></textarea>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                    
                    <div class="d-grid">
                        <button type="submit" id="submit-assessment-btn" class="btn btn-success btn-lg">
                            <i class="fas fa-check-circle me-2"></i> 完成并提交考核
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="text-center mt-3">
            <a href="{% url 'student_dashboard' %}" class="btn btn-outline-secondary">返回我的考核列表</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('assessment-form').addEventListener('submit', function() {
            // Show the loading overlay
            document.getElementById('loading-overlay').style.display = 'flex';
            
            // Disable the submit button to prevent multiple submissions
            const submitBtn = document.getElementById('submit-assessment-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 正在提交...';
        });
    </script>
</body>
</html> 