<!DOCTYPE html>
<html>
<head>
    <title>我的考核</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0"><i class="fas fa-tasks me-2"></i> 我的考核</h4>
        </div>
        <div class="list-group list-group-flush">
            {% if assessments %}
                {% for assessment in assessments %}
                    <a href="{% if assessment.id not in completed_assessment_ids %}{% url 'student_dashboard:assessment_detail' assessment.id %}{% else %}{% url 'student_dashboard:assessment_result' assessment.id %}{% endif %}"
                       target="_parent" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ assessment.title }}</h6>
                            <small class="text-muted">发布于: {{ assessment.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        {% if assessment.id in completed_assessment_ids %}
                            <span class="badge bg-success rounded-pill"><i class="fas fa-check-circle me-1"></i>已完成，查看结果</span>
                        {% else %}
                            <span class="badge bg-warning text-dark rounded-pill">待完成</span>
                        {% endif %}
                    </a>
                {% endfor %}
            {% else %}
                <div class="list-group-item">
                    <p class="text-center text-muted my-3">老师还没有发布任何考核任务。</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html> 