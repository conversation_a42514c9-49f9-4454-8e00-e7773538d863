<!DOCTYPE html>
<html>
<head>
    <title>在线学习助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <style>
        body { background-color: #f8f9fa; }
        .wrong-question-card {
            transition: opacity 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Module 1: AI Learning Assistant -->
        <div class="mb-5">
            <h1 class="mb-3">AI 学习助手</h1>
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="student_question" class="form-label"><strong>向AI提问:</strong></label>
                            <textarea class="form-control" id="student_question" name="student_question" rows="3" placeholder="在这里输入你遇到的学习问题...">{{ request.POST.student_question }}</textarea>
                        </div>
                        <button type="submit" id="ask-ai-btn" class="btn btn-primary">
                            <span class="btn-text"><i class="fas fa-paper-plane me-2"></i>提交问题</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                            <span class="loading-text d-none">正在思考中...</span>
                        </button>
                    </form>
                    {% if ai_response %}
                    <hr class="my-4">
                    <div class="mt-3">
                        <h5>AI的回答:</h5>
                        <div class="p-3 bg-light border rounded">
                            <p style="white-space: pre-wrap;">{{ ai_response }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Module 2: Wrong Questions Notebook -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">我的错题本</h1>
            <p class="lead mb-0 text-muted">温故而知新，可以为师矣。</p>
        </div>
        <div id="wrong-questions-container">
            {% for response in wrong_questions %}
                <div class="card mb-3 wrong-question-card" id="response-card-{{ response.id }}">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <div>
                            <strong>问题ID: {{ response.question.id }}</strong>
                            <small class="ms-2 text-muted">出自: {{ response.assessment.title }}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-success master-btn" data-response-id="{{ response.id }}">
                            <i class="fas fa-check-circle me-1"></i> 我已掌握
                        </button>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-2"><strong>题目：</strong>{{ response.question.question_text|linebreaksbr }}</p>
                        {% if response.question.options %}
                            <div class="mb-2">
                                {% for option in response.question.options %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" disabled>
                                        <label class="form-check-label">{{ option }}</label>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <p><strong>你的答案:</strong> <span class="text-danger fw-bold">{{ response.answer }}</span></p>
                        <p><strong>正确答案:</strong> <span class="text-success fw-bold">{{ response.question.correct_answer }}</span></p>
                        {% if response.explanation %}
                            <hr>
                            <div class="mt-2">
                                <p class="mb-1"><strong><i class="fas fa-lightbulb text-warning me-1"></i>AI智能解析:</strong></p>
                                <p class="mb-0 alert alert-light" style="white-space: pre-wrap;">{{ response.explanation }}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-trophy fa-3x text-success"></i>
                    <h4 class="mt-3">太棒了！</h4>
                    <p class="text-muted">你的错题本是空的，继续保持！</p>
                </div>
            {% endfor %}
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // AI Assistant Form Submission
    const aiForm = document.querySelector('form[method="post"]');
    if (aiForm) {
        aiForm.addEventListener('submit', function() {
            const askBtn = document.getElementById('ask-ai-btn');
            const btnText = askBtn.querySelector('.btn-text');
            const spinner = askBtn.querySelector('.spinner-border');
            const loadingText = askBtn.querySelector('.loading-text');

            askBtn.disabled = true;
            btnText.classList.add('d-none');
            spinner.classList.remove('d-none');
            loadingText.classList.remove('d-none');
        });
    }

    // Wrong Questions Notebook "Master" button logic
    const csrftoken = document.querySelector('[name=csrf-token]').getAttribute('content');

    document.querySelectorAll('.master-btn').forEach(button => {
        button.addEventListener('click', function () {
            const responseId = this.dataset.responseId;
            const card = document.getElementById('response-card-' + responseId);

            fetch(`{% url 'student_dashboard:master_question' 0 %}`.replace('0', responseId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrftoken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('网络请求失败。');
            })
            .then(data => {
                if (data.status === 'success') {
                    card.style.opacity = '0';
                    setTimeout(() => {
                        card.remove();
                        // Check if container is empty
                        if (document.getElementById('wrong-questions-container').childElementCount === 0) {
                            document.getElementById('wrong-questions-container').innerHTML = `
                                <div class="text-center py-5">
                                    <i class="fas fa-trophy fa-3x text-success"></i>
                                    <h4 class="mt-3">太棒了！</h4>
                                    <p class="text-muted">你的错题本是空的，继续保持！</p>
                                </div>`;
                        }
                    }, 500);
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作时发生错误。');
            });
        });
    });
});
</script>
</body>
</html> 