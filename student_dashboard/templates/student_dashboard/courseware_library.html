<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<style>
    body {
        background-color: #f8f9fa;
        padding: 1.5rem;
    }
    .card.shadow {
        box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
    }
    .subject-header {
        border-left: 5px solid #0d6efd;
        padding-left: 15px;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
</style>

<div class="container-fluid">
    <!-- Page Heading -->
    <h1 class="h3 mb-4 text-gray-800">学习资源库</h1>

    {% if courseware_by_subject %}
        {% for subject, coursewares in courseware_by_subject.items %}
            <h2 class="h5 subject-header">{{ subject }}</h2>
            <div class="row">
                {% for item in coursewares %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card shadow h-100">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ item.title }}</h5>
                            <p class="card-text text-muted small">由 {{ item.teacher.username }} 老师上传于 {{ item.upload_date|date:"Y-m-d" }}</p>
                            <p class="card-text flex-grow-1">{{ item.description|default:'暂无描述' }}</p>
                            <a href="{{ item.file.url }}" class="btn btn-primary mt-auto" target="_blank">
                                <i class="fas fa-download fa-sm"></i> 下载资源
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% endfor %}
    {% else %}
        <div class="card shadow">
            <div class="card-body text-center">
                <p class="lead">目前还没有任何学习资源。</p>
                <p class="text-muted">请等待教师上传课件。</p>
            </div>
        </div>
    {% endif %}
</div> 