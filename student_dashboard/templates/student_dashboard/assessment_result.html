<!DOCTYPE html>
<html>
<head>
    <title>考核结果 - {{ assessment.title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body { background-color: #f0f2f5; }
        .result-card { border-left-width: 5px; }
        .border-success { border-left-color: #198754 !important; }
        .border-danger { border-left-color: #dc3545 !important; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card shadow-sm">
            <div class="card-header bg-dark text-white">
                <h2 class="mb-0">考核结果: {{ assessment.title }}</h2>
            </div>
            <div class="card-body">
                <div class="row text-center mb-4">
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h4>得分情况</h4>
                            <p class="display-4">{{ correct_count }} / {{ total_questions }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h4>正确率</h4>
                            <p class="display-4">{% widthratio correct_count total_questions 100 %}%</p>
                        </div>
                    </div>
                </div>

                <hr>

                {% for result in results %}
                    <div class="card mb-3 result-card {% if result.is_correct %}border-success{% else %}border-danger{% endif %}">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <strong>问题 {{ forloop.counter }}: 【{{ result.question.question_type }}】</strong>
                            {% if result.is_correct %}
                                <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> 正确</span>
                            {% else %}
                                <span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i> 错误</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <p class="card-text">{{ result.question.question_text|linebreaksbr }}</p>
                            <p><strong>你的答案:</strong> <span class="text-primary">{{ result.student_answer }}</span></p>
                            
                            {% if not result.is_correct %}
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#answer-{{ result.question.id }}" aria-expanded="false">
                                    <i class="fas fa-eye me-1"></i> 查看正确答案
                                </button>
                                <div class="collapse mt-2" id="answer-{{ result.question.id }}">
                                    <div class="card card-body bg-light">
                                        <strong>正确答案:</strong> <span class="text-success">{{ result.question.answer }}</span>
                                        {% if result.explanation %}
                                        <hr>
                                        <div class="mt-2">
                                            <p class="mb-1"><strong><i class="fas fa-lightbulb text-warning me-1"></i>答案解析:</strong></p>
                                            <p class="mb-0" style="white-space: pre-wrap;">{{ result.explanation }}</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        <div class="text-center mt-3">
            <a href="{% url 'student_dashboard' %}" class="btn btn-primary">返回我的考核列表</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 