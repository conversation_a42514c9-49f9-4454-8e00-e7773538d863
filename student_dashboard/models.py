from django.db import models
from users.models import User
from teacher_dashboard.models import Assessment, Question

# Create your models here.

class AssessmentAttempt(models.Model):
    """
    Represents a single attempt by a student to take an assessment.
    """
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assessment_attempts')
    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='attempts')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    score = models.FloatField(null=True, blank=True)

    def __str__(self):
        return f"{self.student.username}'s attempt on {self.assessment.title}"

class StudentAnswer(models.Model):
    """
    Stores a student's answer to a specific question within an assessment attempt.
    """
    assessment_attempt = models.ForeignKey(AssessmentAttempt, on_delete=models.CASCADE, related_name='student_answers')
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='student_answers')
    answer_text = models.TextField(blank=True, null=True)
    is_correct = models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)

    def __str__(self):
        return f"Answer by {self.assessment_attempt.student.username} for Q{self.question.id} in {self.assessment_attempt.assessment.title}"
