#!/usr/bin/env python
"""
Debug script to test chart data generation without running the full Django server
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Downloads/项目/AI教学实训模型系统')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

# Now import Django models
from teacher_dashboard.models import Question, Assessment
from student_dashboard.models import AssessmentAttempt, StudentAnswer
from users.models import User
from django.db.models import Count
import json

def test_chart_data():
    print("=== Testing Chart Data Generation ===")
    
    # Test Question Type Distribution
    print("\n1. Question Type Distribution:")
    question_type_distribution = Question.objects.values('question_type').annotate(count=Count('id'))
    print(f"Raw query result: {list(question_type_distribution)}")
    
    type_map = dict(Question.QUESTION_TYPE_CHOICES)
    print(f"Type map: {type_map}")
    
    question_type_data = {
        type_map.get(item['question_type'], item['question_type']): item['count'] 
        for item in question_type_distribution
    }
    print(f"Processed data: {question_type_data}")
    
    # If no data exists, provide sample data
    if not question_type_data:
        question_type_data = {
            '选择题': 0,
            '判断题': 0,
            '简答题': 0,
            '编程题': 0
        }
        print(f"Using fallback data: {question_type_data}")
    
    print(f"JSON output: {json.dumps(question_type_data)}")
    
    # Test Assessment Accuracy Trend
    print("\n2. Assessment Accuracy Trend:")
    
    # Get a sample teacher (first user with teacher role)
    teachers = User.objects.filter(role='teacher')
    if teachers.exists():
        teacher = teachers.first()
        print(f"Using teacher: {teacher.username}")
        
        recent_assessments = Assessment.objects.filter(teacher=teacher, is_published=True).order_by('-created_at')[:5]
        print(f"Found {recent_assessments.count()} assessments")
        
        assessment_accuracy_labels = []
        assessment_accuracy_data = []
        
        for assessment in reversed(recent_assessments):
            attempts = AssessmentAttempt.objects.filter(assessment=assessment)
            print(f"Assessment '{assessment.title}': {attempts.count()} attempts")
            
            if attempts.exists():
                correct_answers = StudentAnswer.objects.filter(
                    assessment_attempt__in=attempts, 
                    is_correct=True
                ).count()
                total_questions_in_assessment = assessment.questions.count()
                total_attempts = attempts.count()
                
                if total_questions_in_assessment > 0 and total_attempts > 0:
                    accuracy = (correct_answers / (total_questions_in_assessment * total_attempts)) * 100
                else:
                    accuracy = 0
                    
                print(f"  - Correct answers: {correct_answers}")
                print(f"  - Total questions: {total_questions_in_assessment}")
                print(f"  - Total attempts: {total_attempts}")
                print(f"  - Accuracy: {accuracy}%")
            else:
                accuracy = 0
                print(f"  - No attempts, accuracy: 0%")
            
            assessment_accuracy_labels.append(assessment.title)
            assessment_accuracy_data.append(round(accuracy, 2))
        
        # If no assessment data exists, provide sample data
        if not assessment_accuracy_labels:
            assessment_accuracy_labels = ['暂无考核数据']
            assessment_accuracy_data = [0]
            print("Using fallback assessment data")
        
        print(f"Labels: {assessment_accuracy_labels}")
        print(f"Data: {assessment_accuracy_data}")
        print(f"Labels JSON: {json.dumps(assessment_accuracy_labels)}")
        print(f"Data JSON: {json.dumps(assessment_accuracy_data)}")
    else:
        print("No teachers found in database")
    
    # Test database counts
    print("\n3. Database Counts:")
    print(f"Total questions: {Question.objects.count()}")
    print(f"Total assessments: {Assessment.objects.count()}")
    print(f"Total assessment attempts: {AssessmentAttempt.objects.count()}")
    print(f"Total student answers: {StudentAnswer.objects.count()}")
    print(f"Total users: {User.objects.count()}")
    print(f"Teachers: {User.objects.filter(role='teacher').count()}")
    print(f"Students: {User.objects.filter(role='student').count()}")

if __name__ == '__main__':
    test_chart_data()
