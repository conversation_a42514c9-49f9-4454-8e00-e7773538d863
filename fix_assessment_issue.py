#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.db import connection
from teacher_dashboard.models import StudentResponse

def fix_database_issue():
    """修复数据库字段问题"""
    print("=== 修复数据库字段问题 ===")
    
    # 检查is_mastered字段是否存在
    with connection.cursor() as cursor:
        cursor.execute("PRAGMA table_info(teacher_dashboard_studentresponse);")
        columns = [row[1] for row in cursor.fetchall()]
        
        print(f"当前表字段: {columns}")
        
        if 'is_mastered' not in columns:
            print("❌ is_mastered字段不存在，需要添加")
            try:
                cursor.execute("ALTER TABLE teacher_dashboard_studentresponse ADD COLUMN is_mastered BOOLEAN DEFAULT 0;")
                print("✅ 成功添加is_mastered字段")
            except Exception as e:
                print(f"❌ 添加字段失败: {e}")
        else:
            print("✅ is_mastered字段已存在")
            
        if 'explanation' not in columns:
            print("❌ explanation字段不存在，需要添加")
            try:
                cursor.execute("ALTER TABLE teacher_dashboard_studentresponse ADD COLUMN explanation TEXT;")
                print("✅ 成功添加explanation字段")
            except Exception as e:
                print(f"❌ 添加字段失败: {e}")
        else:
            print("✅ explanation字段已存在")

if __name__ == '__main__':
    fix_database_issue()
