from django.urls import path
from . import views

app_name = 'admin_dashboard'

urlpatterns = [
    # Main dashboard view
    path('dashboard/', views.admin_dashboard, name='dashboard'),
    
    # Overview - now aliased to the main dashboard view
    path('overview/', views.overview_dashboard_view, name='overview_dashboard'),
    
    # User management
    path('teachers/', views.teacher_management, name='teacher_management'),
    path('students/', views.student_management, name='student_management'),
    path('manage_user/', views.manage_user, name='manage_user'),
    path('delete-user/<int:user_id>/', views.delete_user, name='delete_user'),

    # Courseware management
    path('courseware/', views.courseware_management_view, name='courseware_management'),
    path('courseware/delete/<int:courseware_id>/', views.delete_courseware_view, name='delete_courseware'),
    path('courseware/edit/<int:courseware_id>/', views.edit_courseware_view, name='edit_courseware'),

    # Question and analysis
    path('question/<int:question_id>/', views.question_detail, name='question_detail'),
    path('all_wrong_questions/', views.all_wrong_questions, name='all_wrong_questions'),

    # Demo page
    path('demo/', views.demo_page, name='demo_page'),
]