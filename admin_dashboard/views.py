from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from users.models import User
from teacher_dashboard.models import Assessment, Question, StudentResponse, Courseware
from django.db.models import Count, Avg, Case, When, F, FloatField, Q
import json
from django.utils.timezone import now
from datetime import timedelta
from django.db.models.functions import TruncDate
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST

# Create your views here.

@login_required
def admin_dashboard(request):
    # 根据用户角色进行智能重定向
    if request.user.role == "teacher":
        return redirect("teacher_dashboard:dashboard")
    elif request.user.role == "student":
        return redirect("student_dashboard:dashboard")
    elif request.user.role != "admin":
        # 对于未知角色，重定向到登录页面
        return redirect("users:login")
    
    # The main dashboard view no longer needs to calculate all the details.
    # It just provides the main page structure. The overview data will be loaded via AJAX.
    return render(request, "admin_dashboard/dashboard.html")


@login_required
def overview_dashboard_view(request):
    """
    This view provides the data and renders the HTML for the overview dashboard content,
    which is loaded via AJAX into the main dashboard page.
    """
    if request.user.role != "admin":
        return redirect("users:login")

    # --- Start of dashboard data logic ---
    total_students = User.objects.filter(role='student', is_staff=False).count()
    total_teachers = User.objects.filter(role='teacher', is_staff=False).count()
    total_assessments = Assessment.objects.count()
    total_questions_in_bank = Question.objects.count()

    # Top Wrong Questions - this is still needed
    top_wrong_questions = StudentResponse.objects.filter(is_correct=False).values(
        'question_id', 'question__question_text'
    ).annotate(
        wrong_count=Count('id')
    ).order_by('-wrong_count')[:5]

    context = {
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_assessments': total_assessments,
        'total_questions_in_bank': total_questions_in_bank,
        'top_wrong_questions': list(top_wrong_questions),
        # 'chart_data_json' is no longer needed as charts are removed
    }
    
    return render(request, "admin_dashboard/overview_dashboard.html", context)


@login_required
def teacher_management(request):
    if request.user.role != "admin":
        return redirect("home")
    
    teachers = User.objects.filter(role='teacher').exclude(is_staff=True).order_by('username')
    
    # 添加分页功能
    paginator = Paginator(teachers, 20)  # 每页显示20条记录
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    return render(request, "admin_dashboard/teacher_management.html", {
        "users": page_obj,
        "user_type": "教师",
        "page_obj": page_obj
    })

@login_required
def student_management(request):
    if request.user.role != "admin":
        return redirect("home")
    
    students = User.objects.filter(role='student').exclude(is_staff=True).order_by('username')
    
    # 重新启用分页功能
    paginator = Paginator(students, 20)  # 每页显示20条记录
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    return render(request, "admin_dashboard/student_management.html", {
        "users": page_obj,
        "user_type": "学生",
        "page_obj": page_obj
    })

@login_required
def manage_user(request):
    if request.method != 'POST' or request.user.role != 'admin':
        return redirect('admin_dashboard')
    
    is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'

    action = request.POST.get('action', 'save')
    user_id = request.POST.get('user_id')
    role = request.POST.get('user_role', 'student') 
    
    try:
        if action == 'delete':
            user = User.objects.get(id=user_id)
            if user.role == 'admin' or user.is_staff:
                if is_ajax:
                    return JsonResponse({'status': 'error', 'message': 'Cannot delete an admin or staff user.'}, status=403)
            else:
                user.delete()
        else: # Save (Create or Update)
            username = request.POST.get('username')
            email = request.POST.get('email')
            password = request.POST.get('password')

            if user_id: # Update
                user = User.objects.get(id=user_id)
                if user.role == 'admin' or user.is_staff:
                    if is_ajax:
                        return JsonResponse({'status': 'error', 'message': 'Cannot edit an admin or staff user.'}, status=403)
                else:
                    user.username = username
                    user.email = email
                    if password:
                        user.set_password(password)
                    user.save()
            else: # Create
                if User.objects.filter(username=username).exists():
                    if is_ajax:
                        return JsonResponse({'status': 'error', 'message': f'Username "{username}" already exists.'}, status=400)
                
                user = User.objects.create_user(username=username, email=email, password=password)
                user.role = role
                user.save()

        if is_ajax:
            return JsonResponse({'status': 'success', 'message': 'Operation successful.'})

    except User.DoesNotExist:
        if is_ajax:
            return JsonResponse({'status': 'error', 'message': 'User not found.'}, status=404)
    except Exception as e:
        if is_ajax:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    # Fallback for non-AJAX requests
    if role == 'teacher':
        return redirect('admin_dashboard:teacher_management')
    else:
        return redirect('admin_dashboard:student_management')

# This is the old, simulated view. It will be removed.
# @login_required
# def courseware_management(request):
#     if request.user.role != "admin":
#         return redirect("home")
# 
#     # Simulate courseware resources
#     courseware_resources = [
#         {"id": 1, "name": "Python基础", "type": "PDF", "subject": "计算机"},
#         {"id": 2, "name": "Django开发", "type": "DOCX", "subject": "计算机"},
#         {"id": 3, "name": "线性代数", "type": "视频", "subject": "数学"},
#     ]
#     return render(request, "admin_dashboard/courseware_management.html", {"courseware_resources": courseware_resources})


@login_required
def question_detail(request, question_id):
    if request.user.role != "admin":
        return redirect("home")

    try:
        question = Question.objects.get(pk=question_id)
        # 获取所有对该问题的错误回答
        wrong_responses = StudentResponse.objects.filter(question=question, is_correct=False).select_related('student')
    except Question.DoesNotExist:
        # 如果问题不存在，可以重定向或显示404页面
        # 这里为了简单，我们重定向回概览页
        return redirect('overview_dashboard')

    context = {
        'question': question,
        'wrong_responses': wrong_responses
    }
    return render(request, 'admin_dashboard/question_detail.html', context)


@login_required
def all_wrong_questions(request):
    if request.user.role != "admin":
        return redirect("home")

    wrong_questions_list = StudentResponse.objects.filter(is_correct=False).values(
        'question_id', 'question__question_text'
    ).annotate(
        wrong_count=Count('id')
    ).order_by('-wrong_count')

    paginator = Paginator(wrong_questions_list, 15)  # 每页显示15条
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'admin_dashboard/all_wrong_questions.html', {'page_obj': page_obj})

@login_required
def courseware_management_view(request):
    if not request.user.is_superuser:
        return redirect('users:login')

    queryset = Courseware.objects.all().select_related('teacher').order_by('-upload_date')
    
    # Get filter parameters from request
    search_query = request.GET.get('q', '')
    subject_filter = request.GET.get('subject', '')
    teacher_filter = request.GET.get('teacher', '')

    # Apply filters
    if search_query:
        queryset = queryset.filter(
            Q(title__icontains=search_query) | Q(description__icontains=search_query)
        )
    if subject_filter:
        queryset = queryset.filter(subject=subject_filter)
    if teacher_filter:
        queryset = queryset.filter(teacher__id=teacher_filter)

    # For populating filter dropdowns
    subjects = Courseware.objects.values_list('subject', flat=True).distinct().order_by('subject')
    teachers = User.objects.filter(is_teacher=True).order_by('username')
    
    context = {
        'coursewares': queryset,
        'subjects': subjects,
        'teachers': teachers,
        'current_filters': {
            'q': search_query,
            'subject': subject_filter,
            'teacher': teacher_filter,
        }
    }
    return render(request, 'admin_dashboard/courseware_management.html', context)

@require_POST
@login_required
def delete_courseware_view(request, courseware_id):
    if not request.user.is_superuser:
        return redirect('users:login')
    
    courseware = get_object_or_404(Courseware, id=courseware_id)
    courseware.delete()
    
    # After deletion, redirect back to the courseware management page
    return redirect('admin_dashboard:courseware_management')

@login_required
def edit_courseware_view(request, courseware_id):
    if not request.user.is_superuser:
        return redirect('users:login')
        
    courseware = get_object_or_404(Courseware, id=courseware_id)

    if request.method == 'POST':
        # Update the courseware object with data from the form
        courseware.title = request.POST.get('title', courseware.title)
        courseware.subject = request.POST.get('subject', courseware.subject)
        courseware.description = request.POST.get('description', courseware.description)
        
        # Optionally handle file change
        if 'file' in request.FILES:
            courseware.file = request.FILES['file']
            
        courseware.save()
        return redirect('admin_dashboard:courseware_management')

    # For GET request, render the edit form
    return render(request, 'admin_dashboard/edit_courseware.html', {'courseware': courseware})

@require_POST
@login_required
def delete_user(request, user_id):
    if request.user.role != 'admin':
        return redirect("home")
    
    user_to_delete = get_object_or_404(User, id=user_id)
    
    # Simple safety check: prevent deleting other admins or oneself
    if user_to_delete.role == 'admin' or user_to_delete.is_staff:
        # Optionally, you can add a message here using Django's messaging framework
        # messages.error(request, "无法删除管理员账户。")
        pass # Silently ignore or redirect
    else:
        user_to_delete.delete()
        # messages.success(request, "用户已成功删除。")

    # Determine which page to redirect to based on the deleted user's role
    if user_to_delete.role == 'teacher':
        return redirect('admin_dashboard:teacher_management')
    else:
        return redirect('admin_dashboard:student_management')
