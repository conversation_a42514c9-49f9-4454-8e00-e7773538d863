<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>错题详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
<div class="container mt-5">
    <a href="javascript:history.back()" class="btn btn-secondary mb-4"><i class="fas fa-arrow-left me-2"></i> 返回</a>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">问题详情</h4>
        </div>
        <div class="card-body">
            <p class="card-text fs-5">{{ question.question_text }}</p>

            {% if question.question_type == 'multiple_choice' and question.options %}
                <h6 class="mt-4">选项:</h6>
                <ul class="list-group">
                    {% for option in question.options %}
                        <li class="list-group-item">{{ option }}</li>
                    {% endfor %}
                </ul>
            {% endif %}

            <hr>
            <p><strong>正确答案:</strong> <span class="badge bg-success fs-6">{{ question.answer }}</span></p>
            <p><strong>题目类型:</strong> <span class="badge bg-info fs-6">{{ question.get_question_type_display }}</span></p>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title">答错学生列表 ({{ wrong_responses.count }})</h5>
        </div>
        <div class="card-body">
            {% if wrong_responses %}
                <ul class="list-group">
                    {% for response in wrong_responses %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-user me-2"></i>
                                <strong>{{ response.student.username }}</strong>
                            </div>
                            <div>
                                <span class="me-3">Ta的回答: <span class="badge bg-danger">{{ response.answer }}</span></span>
                                <span>提交于: {{ response.submitted_at|date:"Y-m-d H:i" }}</span>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p class="text-muted">暂无学生答错此题。</p>
            {% endif %}
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 