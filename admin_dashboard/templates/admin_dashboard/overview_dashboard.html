<div class="container-fluid px-4 py-4">
    <!-- Key Performance Indicators -->
    <div class="row">
        <!-- Totals -->
        <div class="col-xl-3 col-md-6 mb-4"><div class="card border-left-primary shadow h-100"><div class="card-body kpi-card-body"><div class="row no-gutters align-items-center"><div class="col mr-2"><div class="text-xs font-weight-bold text-primary text-uppercase mb-1">学生总数</div><div class="h5 mb-0 font-weight-bold">{{ total_students }}</div></div><div class="col-auto"><i class="fas fa-user-graduate fa-2x text-muted"></i></div></div></div></div></div>
        <div class="col-xl-3 col-md-6 mb-4"><div class="card border-left-success shadow h-100"><div class="card-body kpi-card-body"><div class="row no-gutters align-items-center"><div class="col mr-2"><div class="text-xs font-weight-bold text-success text-uppercase mb-1">教师总数</div><div class="h5 mb-0 font-weight-bold">{{ total_teachers }}</div></div><div class="col-auto"><i class="fas fa-chalkboard-teacher fa-2x text-muted"></i></div></div></div></div></div>
        <div class="col-xl-3 col-md-6 mb-4"><div class="card border-left-info shadow h-100"><div class="card-body kpi-card-body"><div class="row no-gutters align-items-center"><div class="col mr-2"><div class="text-xs font-weight-bold text-info text-uppercase mb-1">考核总数</div><div class="h5 mb-0 font-weight-bold">{{ total_assessments }}</div></div><div class="col-auto"><i class="fas fa-clipboard-list fa-2x text-muted"></i></div></div></div></div></div>
        <div class="col-xl-3 col-md-6 mb-4"><div class="card border-left-warning shadow h-100"><div class="card-body kpi-card-body"><div class="row no-gutters align-items-center"><div class="col mr-2"><div class="text-xs font-weight-bold text-warning text-uppercase mb-1">题库总数</div><div class="h5 mb-0 font-weight-bold">{{ total_questions_in_bank }}</div></div><div class="col-auto"><i class="fas fa-book fa-2x text-muted"></i></div></div></div></div></div>
    </div>

    <!-- Main Charts Row -->
    <div class="row">
        <!-- Overall Accuracy & Question Types -->
        <div class="col-lg-5 mb-4">
            <div class="card shadow mb-4"><div class="card-header py-3"><h6 class="m-0 font-weight-bold text-primary">教学效率指数 (综合学习正确率)</h6></div>
                <div class="card-body text-center"><div class="gauge-chart-container"><canvas id="overallAccuracyChart"></canvas></div></div>
            </div>
            <div class="card shadow"><div class="card-header py-3"><h6 class="m-0 font-weight-bold text-primary">题库题型分布</h6></div>
                <div class="card-body"><div class="chart-container" style="height:280px"><canvas id="questionTypeChart"></canvas></div></div>
            </div>
        </div>

        <!-- Teacher Activity & Wrong Questions -->
        <div class="col-lg-7 mb-4">
            <div class="card shadow mb-4"><div class="card-header py-3"><h6 class="m-0 font-weight-bold text-primary">教师活跃度排行 (创建考核数)</h6></div>
                <div class="card-body"><div class="chart-container"><canvas id="teacherActivityChart"></canvas></div></div>
            </div>
            <div class="card shadow"><div class="card-header py-3 d-flex justify-content-between align-items-center"><h6 class="m-0 font-weight-bold text-primary">高频错题</h6><a href="{% url 'admin_dashboard:all_wrong_questions' %}" class="btn btn-sm btn-outline-primary">查看全部</a></div>
                <div class="card-body">
                    {% if top_wrong_questions %}
                        <div class="list-group list-group-flush">
                        {% for item in top_wrong_questions %}<a href="{% url 'admin_dashboard:question_detail' item.question_id %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center text-truncate"><span title="{{ item.question__question_text }}">{{ item.question__question_text }}</span><span class="badge bg-danger rounded-pill">{{ item.wrong_count }} 次</span></a>{% endfor %}
                        </div>
                    {% else %}<p class="text-center text-muted m-0">暂无错题数据</p>{% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Charts Row -->
    <div class="row">
        <div class="col-lg-7 mb-4">
            <div class="card shadow"><div class="card-header py-3"><h6 class="m-0 font-weight-bold text-primary">学生学习效果 (各考核平均正确率)</h6></div>
                <div class="card-body"><div class="chart-container"><canvas id="assessmentPerformanceChart"></canvas></div></div>
            </div>
        </div>
        <div class="col-lg-5 mb-4">
            <div class="card shadow"><div class="card-header py-3"><h6 class="m-0 font-weight-bold text-primary">近期答题活跃度 (近7日)</h6></div>
                <div class="card-body"><div class="chart-container"><canvas id="dailyActivityChart"></canvas></div></div>
            </div>
        </div>
    </div>
</div>
{{ chart_data_json|json_script:"chart_data" }}
<style>
    .border-left-primary { border-left: .25rem solid #4e73df!important; }
    .border-left-success { border-left: .25rem solid #1cc88a!important; }
    .border-left-info { border-left: .25rem solid #36b9cc!important; }
    .border-left-warning { border-left: .25rem solid #f6c23e!important; }
    .chart-container { position: relative; height: 350px; width: 100%; }
    .gauge-chart-container { position: relative; height: 250px; width: 100%; }
    .kpi-card-body { min-height: 105px; }
</style>
