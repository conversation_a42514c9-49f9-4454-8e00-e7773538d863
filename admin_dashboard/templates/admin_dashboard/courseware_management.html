{% extends 'admin_dashboard/dashboard.html' %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">课件资源管理</h1>
    <p class="mb-4">在这里，您可以集中查看和管理由所有教师上传的课件资源。</p>

    <!-- Filter and Search Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">筛选与搜索</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'admin_dashboard:courseware_management' %}">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <input type="text" name="q" class="form-control" placeholder="搜索标题或描述..." value="{{ current_filters.q }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <select name="subject" class="form-select">
                            <option value="">所有学科</option>
                            {% for subject in subjects %}
                                <option value="{{ subject }}" {% if current_filters.subject == subject %}selected{% endif %}>{{ subject }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <select name="teacher" class="form-select">
                            <option value="">所有教师</option>
                            {% for teacher in teachers %}
                                <option value="{{ teacher.id }}" {% if current_filters.teacher == teacher.id|stringformat:"s" %}selected{% endif %}>{{ teacher.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <button type="submit" class="btn btn-primary w-100">筛选</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">所有课件列表</h6>
            <a href="{% url 'admin_dashboard:courseware_management' %}" class="btn btn-sm btn-secondary">重置筛选</a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>学科</th>
                            <th>文件类型</th>
                            <th>上传教师</th>
                            <th>上传日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in coursewares %}
                        <tr>
                            <td>{{ item.id }}</td>
                            <td><a href="{{ item.file.url }}" target="_blank">{{ item.title }}</a></td>
                            <td>{{ item.subject }}</td>
                            <td>{{ item.file.name|slice:"-5:"|upper }}</td>
                            <td>{{ item.teacher.username }}</td>
                            <td>{{ item.upload_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'admin_dashboard:edit_courseware' item.id %}" class="btn btn-warning btn-sm">编辑</a>
                                <form action="{% url 'admin_dashboard:delete_courseware' item.id %}" method="post" style="display: inline;" onsubmit="return confirm('您确定要永久删除这个课件吗？此操作无法撤销。');">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger btn-sm">删除</button>
                                </form>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">系统中还没有任何课件资源。</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 