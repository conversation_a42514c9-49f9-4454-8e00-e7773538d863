{% extends 'admin_dashboard/dashboard.html' %}

{% block content %}
<div class="container-fluid">

    <h1 class="h3 mb-4 text-gray-800">编辑课件信息</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">当前课件: {{ courseware.title }}</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="title" class="form-label">标题</label>
                    <input type="text" class="form-control" id="title" name="title" value="{{ courseware.title }}" required>
                </div>
                <div class="mb-3">
                    <label for="subject" class="form-label">学科</label>
                    <input type="text" class="form-control" id="subject" name="subject" value="{{ courseware.subject }}" required>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">描述</label>
                    <textarea class="form-control" id="description" name="description" rows="3">{{ courseware.description }}</textarea>
                </div>
                <div class="mb-3">
                    <label for="file" class="form-label">替换文件 (可选)</label>
                    <p class="small text-muted">当前文件: <a href="{{ courseware.file.url }}" target="_blank">{{ courseware.file.name }}</a></p>
                    <input class="form-control" type="file" id="file" name="file">
                    <div class="form-text">如果您不上传新文件，将保留原始文件。</div>
                </div>
                <button type="submit" class="btn btn-primary">保存更改</button>
                <a href="{% url 'admin_dashboard:courseware_management' %}" class="btn btn-secondary">返回列表</a>
            </form>
        </div>
    </div>
</div>
{% endblock %} 