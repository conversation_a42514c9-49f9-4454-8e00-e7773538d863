<div class="container-fluid px-4 py-4">
    <h2 class="mb-4">{{ user_type }}管理</h2>
    <p class="lead">欢迎，{{ request.user.username }}！您可以在这里管理系统{{ user_type }}。</p>

    <div class="mb-3">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#userModal" id="addUserBtn">
            <i class="fas fa-plus-circle me-2"></i>添加新{{ user_type }}
        </button>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
            <thead class="table-light">
                <tr>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for u in users %}
                <tr>
                    <td>{{ u.username }}</td>
                    <td>{{ u.email|default:"未设置" }}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-2 edit-btn" data-bs-toggle="modal" data-bs-target="#userModal" 
                                data-id="{{ u.id }}" 
                                data-username="{{ u.username }}"
                                data-email="{{ u.email }}"
                                data-role="{{ u.role }}">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="3" class="text-center">暂无{{ user_type }}数据</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- 添加分页导航 -->
    {% if page_obj.paginator.num_pages > 1 %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div> 