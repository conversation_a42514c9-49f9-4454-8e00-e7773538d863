<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>全部高频错题</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">全部高频错题</h2>
        <a href="{% url 'admin_dashboard:dashboard' %}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i> 返回</a>
    </div>
    
    <div class="card">
        <div class="card-body">
            <ul class="list-group list-group-flush">
                {% for item in page_obj %}
                    <a href="{% url 'question_detail' item.question_id %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        {{ item.question__question_text }}
                        <span class="badge bg-danger rounded-pill">{{ item.wrong_count }} 次</span>
                    </a>
                {% empty %}
                    <li class="list-group-item">暂无错题数据。</li>
                {% endfor %}
            </ul>
        </div>
        <div class="card-footer bg-white">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page=1">&laquo; 首页</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a></li>
                    {% endif %}
                    
                    <li class="page-item disabled"><a class="page-link" href="#" tabindex="-1" aria-disabled="true">第 {{ page_obj.number }} / {{ page_obj.paginator.num_pages }} 页</a></li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a></li>
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页 &raquo;</a></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>
</body>
</html> 