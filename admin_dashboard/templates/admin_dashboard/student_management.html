{% extends "base/dashboard_base.html" %}

{% block title %}{{ user_type }}管理{% endblock %}

{% block initial_content %}
<div class="container-fluid px-4 py-4">
    <h2 class="mb-4">{{ user_type }}管理</h2>
    <p class="lead">您可以在这里管理系统{{ user_type }}。</p>

    <div class="mb-3">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#userModal" id="addUserBtn">
            <i class="fas fa-plus-circle me-2"></i>添加新{{ user_type }}
        </button>
    </div>
    
    <div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
            <thead class="table-light">
                <tr>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for u in users %}
                <tr>
                    <td>{{ u.username }}</td>
                    <td>{{ u.email|default:"未设置" }}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-2 edit-btn" data-bs-toggle="modal" data-bs-target="#userModal" 
                                data-id="{{ u.id }}" 
                                data-username="{{ u.username }}"
                                data-email="{{ u.email }}"
                                data-role="{{ u.role }}">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                        <form action="{% url 'admin_dashboard:delete_user' u.id %}" method="post" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('您确定要删除这位{{ user_type }}吗？');">
                                <i class="fas fa-trash me-1"></i>删除
                            </button>
                        </form>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="3" class="text-center">暂无{{ user_type }}数据</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">&laquo; 上一页</a></li>
            {% else %}
                <li class="page-item disabled"><a class="page-link" href="#">&laquo; 上一页</a></li>
            {% endif %}
            
            <li class="page-item active"><span class="page-link">第 {{ page_obj.number }} / {{ page_obj.paginator.num_pages }} 页</span></li>
            
            {% if page_obj.has_next %}
                <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页 &raquo;</a></li>
            {% else %}
                <li class.page-item.disabled"><a class="page-link" href="#">下一页 &raquo;</a></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div> 
{% endblock %}

{% block modals %}
<!-- User Add/Edit Modal -->
<div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form id="userForm" action="{% url 'admin_dashboard:manage_user' %}" method="post">
        {% csrf_token %}
        <input type="hidden" name="user_id" id="userId">
        <input type="hidden" name="user_role" id="userRole" value="student">
        <div class="modal-header">
          <h5 class="modal-title" id="userModalLabel">用户信息</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
                <label for="email" class="form-label">邮箱</label>
                <input type="email" class="form-control" id="email" name="email">
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input type="password" class="form-control" id="password" name="password">
                <div class="form-text">仅在创建或需要重置密码时填写。</div>
            </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          <button type="submit" class="btn btn-primary">保存</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // This script is now part of the base template, but if specific logic is needed, it can be here.
        // For now, the modal should be handled by the global script if it's generic enough.
        // If not, we re-add the listener here.
        const userModal = document.getElementById('userModal');
        if (userModal) {
            userModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                const modalTitle = userModal.querySelector('.modal-title');
                const form = document.getElementById('userForm');
                
                const userId = button.getAttribute('data-id');
                
                if (userId) { // Edit mode
                    modalTitle.textContent = '编辑{{ user_type }}信息';
                    form.querySelector('#userId').value = userId;
                    form.querySelector('#username').value = button.getAttribute('data-username');
                    form.querySelector('#email').value = button.getAttribute('data-email');
                    form.querySelector('#userRole').value = button.getAttribute('data-role');
                    form.querySelector('#password').placeholder = "留空则不修改密码";
                } else { // Add mode
                    modalTitle.textContent = '添加新{{ user_type }}';
                    form.reset();
                    form.querySelector('#userId').value = '';
                    // Set correct role based on the page context
                    const userType = "{{ user_type }}";
                    if (userType === "学生") {
                        form.querySelector('#userRole').value = 'student';
                    } else if (userType === "教师") {
                        form.querySelector('#userRole').value = 'teacher';
                    }
                    form.querySelector('#password').placeholder = "";
                }
            });
        }
    });
</script>
{% endblock %} 