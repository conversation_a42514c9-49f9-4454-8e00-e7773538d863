{% extends "base/dashboard_base.html" %}

{% block title %}教育管理系统 - 管理员仪表板{% endblock %}

{% block page_styles %}
<style>
    .border-left-primary { border-left: .25rem solid #4e73df!important; }
    .border-left-success { border-left: .25rem solid #1cc88a!important; }
    .border-left-info { border-left: .25rem solid #36b9cc!important; }
    .border-left-warning { border-left: .25rem solid #f6c23e!important; }
    .chart-container { position: relative; height: 350px; width: 100%; }
    .gauge-chart-container { position: relative; height: 250px; width: 100%; }
    .kpi-card-body { min-height: 105px; }
</style>
{% endblock %}

{% block sidebar_heading %}教育管理系统{% endblock %}

{% block sidebar_links %}
    <a href="{% url 'admin_dashboard:dashboard' %}" class="list-group-item list-group-item-action active" data-target-url="{% url 'admin_dashboard:overview_dashboard' %}">
        <div><i class="fas fa-tachometer-alt"></i> 大屏概览</div>
    </a>
    <a href="#" class="list-group-item list-group-item-action" data-target-url="{% url 'admin_dashboard:teacher_management' %}">
        <div><i class="fas fa-chalkboard-teacher"></i> 教师管理</div>
    </a>
    <a href="#" class="list-group-item list-group-item-action" data-target-url="{% url 'admin_dashboard:student_management' %}">
        <div><i class="fas fa-user-graduate"></i> 学生管理</div>
    </a>
    <a href="#" class="list-group-item list-group-item-action" data-target-url="{% url 'admin_dashboard:courseware_management' %}">
        <div><i class="fas fa-book"></i> 课件资源管理</div>
    </a>
{% endblock %}

{% block initial_content %}
    {# This area is now populated by an AJAX call in dashboard_base.html #}
    {# The initial URL is set on the '大屏概览' link's data-target-url attribute #}
    <div class="spinner-border" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
{% endblock %}

{% block modals %}
    <!-- User Add/Edit Modal (shared across management pages) -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="userModalLabel">用户信息</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form id="userForm" method="post" action="{% url 'admin_dashboard:manage_user' %}">
                {% csrf_token %}
                <input type="hidden" name="user_id" id="userId">
                <input type="hidden" name="user_role" id="userRole">
                
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">邮箱</label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" name="password">
                    <small class="form-text text-muted">仅在需要设置或重置密码时填写。</small>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">保存更改</button>
                    <button type="button" class="btn btn-danger" id="deleteUserBtn" style="display: none;">删除用户</button>
                </div>
            </form>
          </div>
        </div>
      </div>
    </div>
{% endblock %}

{% block dynamic_listeners_init %}
    // The main entry point for initializing all interactive elements on the dashboard.
    function initializeAllDashboardScripts() {
        // Render charts for the overview page
        if (typeof initializeOverviewCharts === 'function') {
            initializeOverviewCharts();
        }
        // Set up event listeners for user management modals and buttons
        if (typeof initializeUserManagementListeners === 'function') {
            initializeUserManagementListeners();
        }
    }
{% endblock %}


{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    // Global function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Function to initialize charts on the overview dashboard
    function initializeOverviewCharts() {
        const chartDataEl = document.getElementById('chart_data');
        if (!chartDataEl) return;
        const chartData = JSON.parse(chartDataEl.textContent);

        // Clear previous charts if any, to prevent conflicts on AJAX reload
        const chartCanvasIds = ['overallAccuracyChart', 'questionTypeChart', 'teacherActivityChart', 'assessmentPerformanceChart', 'dailyActivityChart'];
        chartCanvasIds.forEach(id => {
            const chartInstance = Chart.getChart(id);
            if (chartInstance) {
                chartInstance.destroy();
            }
            // Clear the gauge text
            const gaugeText = document.querySelector(`#${id} + .gauge-text-div`);
            if (gaugeText) {
                gaugeText.remove();
            }
        });

        const getColors = (num) => ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796', '#5a5c69'].slice(0, num);

        const renderEmpty = (canvasId, text) => {
            const ctx = document.getElementById(canvasId)?.getContext('2d');
            if (!ctx) return;
            const canvas = ctx.canvas;
            const dpr = window.devicePixelRatio || 1;
            canvas.width = canvas.offsetWidth * dpr;
            canvas.height = canvas.offsetHeight * dpr;
            const c = canvas.getContext('2d');
            c.scale(dpr, dpr);
            c.textAlign = 'center';
            c.font = '16px Arial';
            c.fillStyle = '#858796';
            c.fillText(text, canvas.offsetWidth / 2, canvas.offsetHeight / 2);
        };

        const renderChart = (id, config) => {
            const ctx = document.getElementById(id)?.getContext('2d');
            if (ctx) { new Chart(ctx, config); } else { renderEmpty(id, '图表加载失败'); }
        };
        
        // Accuracy Gauge
        const acc = chartData.overall_accuracy;
        const overallAccuracyCtx = document.getElementById('overallAccuracyChart')?.getContext('2d');
        if (overallAccuracyCtx && acc !== null && acc !== undefined) {
            new Chart(overallAccuracyCtx, {
                type: 'doughnut',
                data: { labels: ['正确率', ''], datasets: [{ data: [acc, 100 - acc], backgroundColor: ['#1cc88a', '#e9ecef'], borderWidth: 0 }] },
                options: { rotation: -90, circumference: 180, cutout: '70%', responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false }, tooltip: { enabled: false } } }
            });
            const gaugeText = document.createElement('div');
            gaugeText.className = 'gauge-text-div'; // Add a class for easy selection
            gaugeText.style.position = 'absolute';
            gaugeText.style.top = '60%';
            gaugeText.style.left = '50%';
            gaugeText.style.transform = 'translate(-50%, -50%)';
            gaugeText.style.textAlign = 'center';
            gaugeText.innerHTML = `<div style="font-size: 2.2rem; font-weight: bold; color: #1cc88a;">${acc}%</div>`;
            overallAccuracyCtx.canvas.parentNode.appendChild(gaugeText);
        } else { renderEmpty('overallAccuracyChart', '数据不足'); }

        // Question Types Pie Chart
        const qtData = chartData.question_type_dist;
        if (qtData && qtData.labels && qtData.labels.length > 0) {
            renderChart('questionTypeChart', { type: 'pie', data: { labels: qtData.labels, datasets: [{ data: qtData.data, backgroundColor: getColors(qtData.labels.length) }] }, options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'right' } } } });
        } else { renderEmpty('questionTypeChart', '暂无题型数据'); }

        // Teacher Activity Bar Chart
        const taData = chartData.teacher_activity;
        if (taData && taData.labels && taData.labels.length > 0) {
            renderChart('teacherActivityChart', { type: 'bar', data: { labels: taData.labels, datasets: [{ label: '创建考核数', data: taData.data, backgroundColor: '#4e73df' }] }, options: { indexAxis: 'y', responsive: true, maintainAspectRatio: false, plugins: { legend: { display: false } } } });
        } else { renderEmpty('teacherActivityChart', '暂无教师数据'); }

        // Assessment Performance Bar Chart
        const apData = chartData.assessment_performance;
        if (apData && apData.labels && apData.labels.length > 0) {
            renderChart('assessmentPerformanceChart', { type: 'bar', data: { labels: apData.labels, datasets: [{ label: '平均正确率', data: apData.data, backgroundColor: '#36b9cc' }] }, options: { responsive: true, maintainAspectRatio: false, scales: { y: { min: 0, max: 100 } }, plugins: { legend: { display: false } } } });
        } else { renderEmpty('assessmentPerformanceChart', '暂无考核数据'); }

        // Daily Activity Line Chart
        const daData = chartData.daily_activity;
        if (daData && daData.labels && daData.labels.length > 0) {
            renderChart('dailyActivityChart', { type: 'line', data: { labels: daData.labels, datasets: [{ label: '每日答题数', data: daData.data, borderColor: '#f6c23e', tension: 0.3 }] }, options: { responsive: true, maintainAspectRatio: false } });
        } else { renderEmpty('dailyActivityChart', '暂无近期活动'); }
    }

    // Function to initialize user management event listeners
    function initializeUserManagementListeners() {
        const userModalEl = document.getElementById('userModal');
        if (!userModalEl) return;

        const userForm = document.getElementById('userForm');
        const userModal = new bootstrap.Modal(userModalEl);
        const modalTitle = userModalEl.querySelector('#userModalLabel');
        const userIdInput = userForm.querySelector('#userId');
        const userRoleInput = userForm.querySelector('#userRole');
        const usernameInput = userForm.querySelector('#username');
        const emailInput = userForm.querySelector('#email');
        const passwordInput = userForm.querySelector('#password');
        const deleteBtn = userModalEl.querySelector('#deleteUserBtn');
        const contentArea = document.getElementById('content-area');

        // 使用事件委托来处理动态加载的按钮
        contentArea.addEventListener('click', function(event) {
            const target = event.target.closest('button');
            if (!target) return;

            if (target.id === 'addUserBtn') {
                const userType = target.closest('.container-fluid').querySelector('h2').textContent.includes('教师') ? '教师' : '学生';
                const userRole = userType === '教师' ? 'teacher' : 'student';
                modalTitle.textContent = `添加新${userType}`;
                userForm.reset();
                userIdInput.value = '';
                userRoleInput.value = userRole;
                passwordInput.placeholder = "请输入新用户的密码";
                deleteBtn.style.display = 'none';
                userModal.show();
            }

            if (target.classList.contains('edit-btn')) {
                const userType = target.closest('.container-fluid').querySelector('h2').textContent.includes('教师') ? '教师' : '学生';
                modalTitle.textContent = `编辑${userType}信息`;
                userForm.reset();
                userIdInput.value = target.dataset.id;
                userRoleInput.value = target.dataset.role;
                usernameInput.value = target.dataset.username;
                emailInput.value = target.dataset.email;
                passwordInput.placeholder = "如需重置密码，请在此输入";
                deleteBtn.style.display = 'block';
                deleteBtn.dataset.id = target.dataset.id;
                deleteBtn.dataset.username = target.dataset.username;
                deleteBtn.dataset.role = target.dataset.role;
                userModal.show();
            }
        });
        
        if (deleteBtn) {
            // 为删除按钮单独添加事件监听器
            deleteBtn.onclick = function() {
                if (confirm(`确定要删除用户 ${this.dataset.username} 吗？`)) {
                    const form = document.createElement('form');
                    form.method = 'post';
                    form.action = `{% url 'admin_dashboard:delete_user' user_id=999 %}`.replace('999', this.dataset.id);
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = getCookie('csrftoken');
                    form.appendChild(csrfInput);
                    document.body.appendChild(form);
                    form.submit();
                }
            };
        }
    }

    // Initial load is now handled by the base template's AJAX logic.
    // We just need to make sure the functions are available globally.
</script>
{% endblock %} 