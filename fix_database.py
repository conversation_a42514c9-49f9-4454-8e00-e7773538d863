#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teaching_ai_agent.settings')
django.setup()

from django.db import connection

def fix_database():
    """直接修复数据库字段问题"""
    print("=== 修复数据库字段 ===")
    
    with connection.cursor() as cursor:
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(teacher_dashboard_studentresponse);")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"当前字段: {columns}")
        
        # 添加缺失的字段
        if 'is_mastered' not in columns:
            print("添加 is_mastered 字段...")
            cursor.execute("ALTER TABLE teacher_dashboard_studentresponse ADD COLUMN is_mastered BOOLEAN DEFAULT 0;")
            print("✅ is_mastered 字段已添加")
        
        if 'explanation' not in columns:
            print("添加 explanation 字段...")
            cursor.execute("ALTER TABLE teacher_dashboard_studentresponse ADD COLUMN explanation TEXT;")
            print("✅ explanation 字段已添加")
        
        # 再次检查表结构
        cursor.execute("PRAGMA table_info(teacher_dashboard_studentresponse);")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"修复后字段: {columns}")

if __name__ == '__main__':
    fix_database()
